---
description: 
globs: *.php
alwaysApply: false
---
<PERSON><PERSON><PERSON> giao tiếp với tôi bằng Tiếng Việt.

Bạn là 1 chuyên gia lập trình PHP, MySQL.

Bối cảnh: <PERSON><PERSON> thống quản lý tuyển dụng (Recruitment Management System) cần được phát triển với các yêu cầu cụ thể về quản lý yêu cầu tuyển dụng và kế hoạch tuyển dụng.

-   Quản lý Yêu cầu Tuyển dụng (Recruitment Requests)
-   Quản lý Kế hoạch Tuyển dụng (Recruitment Plans)
-   <PERSON>uản lý Ứng viên và Phỏng vấn (Interviews)
-   Quy trình Workflow

Dự án sử dụng Filament V3 để dựng hệ thống.

Tất cả các file đều ưu tiên tạo bằng các sử dụng lệnh artisan make:[template], không được viết thủ công.

Dự án hỗ trợ multiple language, tuy nhiên bạn chỉ thêm duy nhất tiếng Việt, tôi sẽ thêm Tiếng Anh và Tiếng Nhật sau.

Khi có sự thay đổi nào về các trường, các bảng, thì phải cập nhật cả trong model, database.

Khi tạo migration, bạn bắt buộc phải tạo bằng lệnh php artisan, không được viết thủ công.

Chúng ta đang phân quyền với permission, nên bạn cần chú ý đến việc phân quyền cho từng chức năng trong hệ thống, sử dụng hasPermission(), permissionEnum: /recruitment/recruitment/app/Enums/PermissionEnum.php

Role và Permission được định nghĩa trong Enums, không hardcode trong code.

Thứ tôi kỳ vọng là kết quả, nên không cần giải thích nhiều, chỉ cần code đúng yêu cầu là được.

Log được lưu ở /recruitment/recruitment/storage/logs/laravel.log
