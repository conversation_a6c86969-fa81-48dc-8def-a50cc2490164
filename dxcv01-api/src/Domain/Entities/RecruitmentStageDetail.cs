﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.Json.Serialization;
using System.Threading.Tasks;

namespace CVAPIService.Domain.Entities;
public class RecruitmentStageDetail
{
    public int Id { get; set; }
    public int CandidateId { get; set; }
    [JsonIgnore]
    public Candidate? Candidate { get; set; }
    public int RecruitmentStageId { get; set; }
    public RecruitmentStage? RecruitmentStage { get; set; }
    public DateOnly? Date {  get; set; }
    public string? Note { get; set; }
}
