﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.Json.Serialization;
using System.Threading.Tasks;

namespace CVAPIService.Domain.Entities;
public class JobDetail
{
    public int Id { get; set; }
    public string? JobTitle { get; set; }
    public int? PartId { get; set; }
    public Part? Part { get; set; }
    public int? LevelId { get; set; }
    public Level? Level { get; set; }
    public DateOnly? StartDate { get; set; }
    public int? StatusId { get; set; }
    public Status? Status { get; set; }
    public DateOnly? DoneTime { get; set; }
    public string? JDFileName { get; set; }
    public int? WorkingFormId { get; set; }
    public WorkingForm? WorkingForm { get; set; }
    public int? JobId { get; set; }
    public Job? Job { get; set; }
    public int? Quantity { get; set; }
    public DateOnly? ClosingDate { get; set; }
    public string? Description { get; set; }
    public DateOnly? DesiredTime { get; set; }
    public int? RecruitmentPlanId { get; set; }
    [JsonIgnore]
    public ICollection<Candidate> Candidates { get; set; } = new List<Candidate>();
    public ICollection<JobDetailAddress> JobDetailAddresses { get; set; } = new List<JobDetailAddress>();
    public bool IsDeleted { get; set; } = false;
    public ICollection<JobDetailAttribute> JobDetailAttributes { get; set; } = new List<JobDetailAttribute>();
}
