﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.Json.Serialization;
using System.Threading.Tasks;

namespace CVAPIService.Domain.Entities;
public class RecruitmentStage
{
    public int Id { get; set; }
    public string? Name { get; set; }
    [JsonIgnore]
    public ICollection<RecruitmentStageDetail> RecruitmentStageDetails { get; set; } = new List<RecruitmentStageDetail>();
}
