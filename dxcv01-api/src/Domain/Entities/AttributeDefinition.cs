﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.Json.Serialization;
using System.Threading.Tasks;

namespace CVAPIService.Domain.Entities;
public class AttributeDefinition
{
    public int Id { get; set; }
    public required string Name { get; set; }
    public required string DataType { get; set; }
    [JsonIgnore]
    public ICollection<CandidateAttribute> CandidateAttributes { get; set; } = new List<CandidateAttribute>();
    [JsonIgnore]
    public ICollection<JobDetailAttribute> JobDetailAttributes { get; set; } = new List<JobDetailAttribute>();
}

