﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.Json.Serialization;
using System.Threading.Tasks;

namespace CVAPIService.Domain.Entities;
public class CandidateAttribute
{
    public int Id { get; set; }
    public required string Value { get; set; }
    public int CandidateId { get; set; }
    [JsonIgnore]
    public Candidate? Candidate { get; set; }
    public int AttributeDefinitionId { get; set; }
    public AttributeDefinition? AttributeDefinition { get; set; }
}
