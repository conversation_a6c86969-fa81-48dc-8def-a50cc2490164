﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using CVAPIService.Application.Common.Interfaces;
using CVAPIService.Domain.Entities;

namespace CVAPIService.Application.Addresses.Queries.GetAddresses;
public record GetAddressesQuery : IRequest<List<Address>>;

public class GetAddressesQueryHandler : IRequestHandler<GetAddressesQuery, List<Address>>
{
    private readonly IApplicationDbContext _context;

    public GetAddressesQueryHandler(IApplicationDbContext context)
    {
        _context = context;
    }

    public async Task<List<Address>> Handle(GetAddressesQuery request, CancellationToken cancellationToken)
    {
        return await _context.Addresses.ToListAsync(cancellationToken);
    }
}

