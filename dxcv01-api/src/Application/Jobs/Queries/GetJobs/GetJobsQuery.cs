﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using CVAPIService.Application.Common.Interfaces;
using CVAPIService.Domain.Entities;

namespace CVAPIService.Application.Jobs.Queries.GetJobs;
public record GetJobsQuery : IRequest<List<Job>>;

public class GetJobsQueryHandler : IRequestHandler<GetJobsQuery, List<Job>>
{
    private readonly IApplicationDbContext _context;

    public GetJobsQueryHandler(IApplicationDbContext context)
    {
        _context = context;
    }

    public async Task<List<Job>> Handle(GetJobsQuery request, CancellationToken cancellationToken)
    {
        return await _context.Jobs.ToListAsync(cancellationToken);
    }
}

