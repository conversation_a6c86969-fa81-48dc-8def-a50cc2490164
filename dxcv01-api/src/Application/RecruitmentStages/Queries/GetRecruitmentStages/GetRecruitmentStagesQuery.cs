﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using CVAPIService.Application.Common.Interfaces;
using CVAPIService.Domain.Entities;

namespace CVAPIService.Application.RecruitmentStages.Queries.GetRecruitmentStages;
public record GetRecruitmentStagesQuery : IRequest<List<RecruitmentStage>>;

public class GetRecruitmentStagesQueryHandler : IRequestHandler<GetRecruitmentStagesQuery, List<RecruitmentStage>>
{
    private readonly IApplicationDbContext _context;

    public GetRecruitmentStagesQueryHandler(IApplicationDbContext context)
    {
        _context = context;
    }

    public async Task<List<RecruitmentStage>> Handle(GetRecruitmentStagesQuery request, CancellationToken cancellationToken)
    {
        return await _context.RecruitmentStages
            .OrderBy(x => x.Id)
            .ToListAsync(cancellationToken);
    }
}
