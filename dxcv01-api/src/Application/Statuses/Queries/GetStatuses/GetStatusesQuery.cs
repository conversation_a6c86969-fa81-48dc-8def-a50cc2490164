﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using CVAPIService.Application.Common.Interfaces;
using CVAPIService.Domain.Entities;

namespace CVAPIService.Application.Statuses.Queries.GetStatuses;
public record GetStatusesQuery : IRequest<List<Status>>;

public class GetStatusesQueryHandler : IRequestHandler<GetStatusesQuery, List<Status>>
{
    private readonly IApplicationDbContext _context;

    public GetStatusesQueryHandler(IApplicationDbContext context)
    {
        _context = context;
    }

    public async Task<List<Status>> Handle(GetStatusesQuery request, CancellationToken cancellationToken)
    {
        return await _context.Statuses.ToListAsync(cancellationToken);
    }
}

