using CVAPIService.Application.Common.Interfaces;
using CVAPIService.Domain.Entities;
using MediatR;
using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using AutoMapper;

namespace CVAPIService.Application.JobDetails.Commands.CreateJobDetail;

public record CreateJobDetailCommand : IRequest<int>
{
    public string? JobTitle { get; set; }
    public int? PartId { get; set; }
    public int? LevelId { get; set; }
    public DateOnly? StartDate { get; set; }
    public int? StatusId { get; set; }
    public string? JDFileName { get; set; }
    public int? WorkingFormId { get; set; }
    public int? JobId { get; set; }
    public List<int>? AddressIds { get; set; }
    public int? Quantity { get; set; }
    public DateOnly? ClosingDate { get; set; }
    public string? Description { get; set; }
    public DateOnly? DesiredTime { get; set; }
    public int? RecruitmentPlanId { get; set; }
    public List<AttributeDefinitionDto>? AttributeDefinitions { get; set; } = new();
    public List<JobDetailAttributeDto>? Attributes { get; set; } = new();
    private class Mapping : Profile
    {
        public Mapping()
        {
            CreateMap<CreateJobDetailCommand, JobDetail>()
                .ForMember(dest => dest.Id, opt => opt.Ignore())
                .ForMember(dest => dest.JobDetailAddresses, opt => opt.Ignore());
        }
    }
}

public record JobDetailAttributeDto
{
    public required string Name { get; set; }
    public required string Value { get; set; }
}
public record AttributeDefinitionDto
{
    public required string Name { get; set; }
    public required string DataType { get; set; }
    private class Mapping : Profile
    {
        public Mapping()
        {
            CreateMap<AttributeDefinitionDto, AttributeDefinition>()
                .ForMember(dest => dest.Id, opt => opt.Ignore());
        }
    }
}

public class CreateJobDetailCommandHandler : IRequestHandler<CreateJobDetailCommand, int>
{
    private readonly IApplicationDbContext _context;

    private readonly IMapper _mapper;

    public CreateJobDetailCommandHandler(IApplicationDbContext context, IMapper mapper)
    {
        _context = context;
        _mapper = mapper;
    }

    public async Task<int> Handle(CreateJobDetailCommand request, CancellationToken cancellationToken)
    {
        var entity = await CreateJobDetailEntity(request, cancellationToken);
        await _context.SaveChangesAsync(cancellationToken);

        var attributeDefinitionIds = await ProcessAttributeDefinitions(request, cancellationToken);
        await AddJobDetailAttributes(request, entity.Id, attributeDefinitionIds, cancellationToken);

        return entity.Id;
    }

    private async Task<JobDetail> CreateJobDetailEntity(CreateJobDetailCommand request, CancellationToken cancellationToken)
    {
        var entity = _mapper.Map<JobDetail>(request);

        foreach (var addressId in request.AddressIds ?? new List<int>())
        {
            entity.JobDetailAddresses.Add(new JobDetailAddress
            {
                AddressId = addressId
            });
        }

        await _context.JobDetails.AddAsync(entity, cancellationToken);
        return entity;
    }

    private async Task<Dictionary<string, int>> ProcessAttributeDefinitions(CreateJobDetailCommand request, CancellationToken cancellationToken)
    {
        var attributeDefinitionIds = new Dictionary<string, int>();

        foreach (var attrDef in request.AttributeDefinitions ?? new List<AttributeDefinitionDto>())
        {
            attributeDefinitionIds[attrDef.Name] = await AddAttributeDefinition(attrDef, cancellationToken);
        }

        return attributeDefinitionIds;
    }

    private async Task AddJobDetailAttributes(CreateJobDetailCommand request, int jobDetailId, Dictionary<string, int> attributeDefinitionIds, CancellationToken cancellationToken)
    {
        foreach (var attr in request.Attributes ?? new List<JobDetailAttributeDto>())
        {
            var jobDetailAttr = new JobDetailAttribute
            {
                JobDetailId = jobDetailId,
                AttributeDefinitionId = attributeDefinitionIds[attr.Name],
                Value = attr.Value
            };
            await _context.JobDetailAttributes.AddAsync(jobDetailAttr);
            await _context.SaveChangesAsync(cancellationToken);
        }
    }

    private async Task<int> AddAttributeDefinition(AttributeDefinitionDto attrDef, CancellationToken cancellationToken)
    {
        var existingAttrDef = await _context.AttributeDefinitions
            .FirstOrDefaultAsync(ad => ad.Name == attrDef.Name && ad.DataType == attrDef.DataType, cancellationToken);

        if (existingAttrDef == null)
        {
            var attributeDefinition = _mapper.Map<AttributeDefinition>(attrDef);
            await _context.AttributeDefinitions.AddAsync(attributeDefinition, cancellationToken);
            await _context.SaveChangesAsync(cancellationToken);
            return attributeDefinition.Id;
        }

        return existingAttrDef.Id;
    }
}
