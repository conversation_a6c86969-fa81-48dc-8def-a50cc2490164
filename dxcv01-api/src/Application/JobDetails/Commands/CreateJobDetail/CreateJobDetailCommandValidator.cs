using CVAPIService.Application.Common.Interfaces;
using CVAPIService.Application.JobDetails.Commands.CreateJobDetail;
using FluentValidation;
using System;
using System.Threading;
using System.Threading.Tasks;
namespace CVAPIService.Application.JobDetails.Commands.CreateJobDetail;
public class CreateJobDetailCommandValidator : AbstractValidator<CreateJobDetailCommand>
{
    private readonly IApplicationDbContext _context;

    public CreateJobDetailCommandValidator(IApplicationDbContext context)
    {
        _context = context;

        RuleFor(v => v.ClosingDate)
            .Must((command, closingDate) =>
                !closingDate.HasValue ||
                !command.StartDate.HasValue ||
                closingDate.Value >= command.StartDate.Value)
            .WithMessage("Closing date must be greater than or equal to start date");
    }
}
