using FluentValidation;
using System;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using CVAPIService.Application.Common.Interfaces;
namespace CVAPIService.Application.JobDetails.Commands.UpdateJobDetail;

public class UpdateJobDetailCommandValidator : AbstractValidator<UpdateJobDetailCommand>
{
    private readonly IApplicationDbContext _context;

    public UpdateJobDetailCommandValidator(IApplicationDbContext context)
    {
        _context = context;

        RuleFor(v => v.ClosingDate)
            .Must((command, closingDate) =>
                !closingDate.HasValue ||
                !command.StartDate.HasValue ||
                closingDate.Value >= command.StartDate.Value)
            .WithMessage("Closing date must be greater than or equal to start date");
    }
}
