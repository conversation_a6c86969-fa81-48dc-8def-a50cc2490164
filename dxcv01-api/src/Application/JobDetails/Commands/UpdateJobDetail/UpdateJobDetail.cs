using CVAPIService.Application.Common.Interfaces;
using CVAPIService.Application.JobDetails.Commands.CreateJobDetail;
using CVAPIService.Domain.Entities;
namespace CVAPIService.Application.JobDetails.Commands.UpdateJobDetail;

public record UpdateJobDetailCommand : IRequest<int>
{
    public required int Id { get; set; }
    public string? JobTitle { get; set; }
    public int? PartId { get; set; }
    public int? LevelId { get; set; }
    public DateOnly? StartDate { get; set; }
    public int? StatusId { get; set; }
    public string? JDFileName { get; set; }
    public int? WorkingFormId { get; set; }
    public int? JobId { get; set; }
    public List<int>? AddressIds { get; set; }
    public int? Quantity { get; set; }
    public DateOnly? ClosingDate { get; set; }
    public string? Description { get; set; }
    public DateOnly? DesiredTime { get; set; }
    public int? RecruitmentPlanId { get; set; }
    public List<AttributeDefinitionDto>? AttributeDefinitions { get; set; } = new();
    public List<JobDetailAttributeDto>? Attributes { get; set; } = new();

    private class Mapping : Profile
    {
        public Mapping()
        {
            CreateMap<UpdateJobDetailCommand, JobDetail>()
                .ForMember(dest => dest.JobDetailAddresses, opt => opt.Ignore());
        }
    }
}

public class UpdateJobDetailCommandHandler : IRequestHandler<UpdateJobDetailCommand, int>
{
    private readonly IApplicationDbContext _context;
    private readonly IMapper _mapper;

    public UpdateJobDetailCommandHandler(IApplicationDbContext context, IMapper mapper)
    {
        _context = context;
        _mapper = mapper;
    }

    public async Task<int> Handle(UpdateJobDetailCommand request, CancellationToken cancellationToken)
    {
        var entity = await GetAndValidateJobDetail(request.Id, cancellationToken);
        await UpdateJobDetailProperties(entity, request, cancellationToken);

        var attributeDefinitionIds = await ProcessAttributeDefinitions(request, cancellationToken);
        await UpdateJobDetailAttributes(request, entity.Id, attributeDefinitionIds, cancellationToken);

        await _context.SaveChangesAsync(cancellationToken);
        return entity.Id;
    }

    private async Task<JobDetail> GetAndValidateJobDetail(int id, CancellationToken cancellationToken)
    {
        var entity = await _context.JobDetails
                          .Include(j => j.JobDetailAddresses)
                          .FirstOrDefaultAsync(j => j.Id == id, cancellationToken);
        if (entity == null)
        {
            throw new NotFoundException(nameof(JobDetail), id.ToString());
        }
        return entity;
    }

    private async Task UpdateJobDetailProperties(JobDetail entity, UpdateJobDetailCommand request, CancellationToken cancellationToken)
    {
        _mapper.Map(request, entity);
        _context.JobDetailAddresses.RemoveRange(entity.JobDetailAddresses);
        await _context.SaveChangesAsync(cancellationToken);
        foreach (var addressId in request.AddressIds ?? new List<int>())
        {
            _context.JobDetailAddresses.Add(new JobDetailAddress
            {
                JobDetailId = entity.Id,
                AddressId = addressId
            });
        }

        if (request.StatusId.HasValue)
        {
            var status = await _context.Statuses.FindAsync(new object[] { request.StatusId.Value }, cancellationToken);
            if (status != null && (status.Name == "Done" || status.Name == "Doing" || status.Name == "Cancel"))
            {
                entity.DoneTime = DateOnly.FromDateTime(DateTime.Now);
            }
        }
    }

    private async Task<Dictionary<string, int>> ProcessAttributeDefinitions(UpdateJobDetailCommand request, CancellationToken cancellationToken)
    {
        var attributeDefinitionIds = new Dictionary<string, int>();

        foreach (var attrDef in request.AttributeDefinitions ?? new List<AttributeDefinitionDto>())
        {
            attributeDefinitionIds[attrDef.Name] = await UpdateAttributeDefinition(attrDef, cancellationToken);
        }

        return attributeDefinitionIds;
    }

    private async Task UpdateJobDetailAttributes(UpdateJobDetailCommand request, int jobDetailId, Dictionary<string, int> attributeDefinitionIds, CancellationToken cancellationToken)
    {
        var existingAttributes = await _context.JobDetailAttributes
            .Where(jda => jda.JobDetailId == jobDetailId)
            .ToListAsync(cancellationToken);

        _context.JobDetailAttributes.RemoveRange(existingAttributes);
        await _context.SaveChangesAsync(cancellationToken);

        foreach (var attr in request.Attributes ?? new List<JobDetailAttributeDto>())
        {
            var jobDetailAttr = new JobDetailAttribute
            {
                JobDetailId = jobDetailId,
                AttributeDefinitionId = attributeDefinitionIds[attr.Name],
                Value = attr.Value
            };
            _context.JobDetailAttributes.Add(jobDetailAttr);
        }
    }
    private async Task<int> UpdateAttributeDefinition(AttributeDefinitionDto attrDef, CancellationToken cancellationToken)
    {
        var existingAttrDef = await _context.AttributeDefinitions
            .FirstOrDefaultAsync(ad => ad.Name == attrDef.Name && ad.DataType == attrDef.DataType, cancellationToken);

        if (existingAttrDef == null)
        {
            var attributeDefinition = _mapper.Map<AttributeDefinition>(attrDef);
            await _context.AttributeDefinitions.AddAsync(attributeDefinition, cancellationToken);
            await _context.SaveChangesAsync(cancellationToken);
            return attributeDefinition.Id;
        }

        return existingAttrDef.Id;
    }
}
