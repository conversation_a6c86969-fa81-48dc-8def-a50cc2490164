﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using CVAPIService.Application.Common.Interfaces;

namespace CVAPIService.Application.JobDetails.Commands.DeleteJobDetail;

public record DeleteJobDetailsCommand : IRequest<Unit>
{
    public List<int>? Ids { get; init; }
}

public class DeleteJobDetailsCommandHandler : IRequestHandler<DeleteJobDetailsCommand, Unit>
{
    private readonly IApplicationDbContext _context;

    public DeleteJobDetailsCommandHandler(IApplicationDbContext context)
    {
        _context = context;
    }

    public async Task<Unit> Handle(DeleteJobDetailsCommand request, CancellationToken cancellationToken)
    {
        if(request != null && request.Ids != null)
        {
            foreach (var id in request.Ids)
            {
                var jobDetail = await _context.JobDetails.FirstOrDefaultAsync(x => x.Id == id);
                if (jobDetail != null)
                {
                    jobDetail.IsDeleted = true;
                }
            }
            await _context.SaveChangesAsync(cancellationToken);
        }
        return Unit.Value;
    }
}
