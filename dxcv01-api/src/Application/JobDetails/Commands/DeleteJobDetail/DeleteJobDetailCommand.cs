﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using CVAPIService.Application.Common.Interfaces;

namespace CVAPIService.Application.JobDetails.Commands.DeleteJobDetail;

public record DeleteJobDetailCommand(int Id) : IRequest;

public class DeleteJobDetailCommandHandler : IRequestHandler<DeleteJobDetailCommand>
{
    private readonly IApplicationDbContext _context;

    public DeleteJobDetailCommandHandler(IApplicationDbContext context)
    {
        _context = context;
    }

    public async Task Handle(DeleteJobDetailCommand request, CancellationToken cancellationToken)
    {
        var jobDetail = await _context.JobDetails.FirstOrDefaultAsync(x => x.Id == request.Id);
        if (jobDetail != null)
        {
            jobDetail.IsDeleted = true;
        }
        await _context.SaveChangesAsync(cancellationToken);
    }
}
