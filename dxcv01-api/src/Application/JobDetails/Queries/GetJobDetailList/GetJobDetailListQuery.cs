using CVAPIService.Application.Common.Interfaces;

public record GetJobDetailListQuery : IRequest<List<JobDetailBriefDto>>;

public class GetJobDetailListQueryHandler : IRequestHandler<GetJobDetailListQuery, List<JobDetailBriefDto>>
{
    private readonly IApplicationDbContext _context;
    private readonly IMapper _mapper;

    public GetJobDetailListQueryHandler(IApplicationDbContext context, IMapper mapper)
    {
        _context = context;
        _mapper = mapper;
    }

    public async Task<List<JobDetailBriefDto>> Handle(GetJobDetailListQuery request, CancellationToken cancellationToken)
    {
        return await _context.JobDetails
            .AsNoTracking()
            .ProjectTo<JobDetailBriefDto>(_mapper.ConfigurationProvider)
            .ToListAsync(cancellationToken);
    }
}
