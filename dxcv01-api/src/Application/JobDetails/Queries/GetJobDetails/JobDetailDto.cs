﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.Json.Serialization;
using System.Threading.Tasks;
using CVAPIService.Domain.Entities;

namespace CVAPIService.Application.JobDetails.Queries.GetJobDetails
{
    public class JobDetailDto
    {
        public int Id { get; set; }
        public string? JobTitle { get; set; }
        public Part? Part { get; set; }
        public Job? Job { get; set; }
        public ICollection<JobDetailAddress>? JobDetailAddresses { get; set; }
        public Level? Level { get; set; }
        public int? Quantity { get; set; }
        public Status? Status { get; set; }
        public WorkingForm? WorkingForm { get; set; }
        public DateOnly? StartDate { get; set; }
        public DateOnly? ClosingDate { get; set; }
        public DateOnly? DesiredTime { get; set; }
        public int? CandidateCount { get; set; }
        public int? RecruitmentPlanId { get; set; }
    }
}
