﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using CVAPIService.Application.Common.Interfaces;
using CVAPIService.Application.Common.Models;
using CVAPIService.Application.JobDetails.Commands.DeleteJobDetail;
using CVAPIService.Domain.Entities;

namespace CVAPIService.Application.JobDetails.Queries.GetJobDetails;

public record GetJobDetailsQuery : IRequest<PaginatedList<JobDetailDto>>
{
    public int PageNumber { get; init; } = 1;
    public int PageSize { get; init; } = 10;
    public List<int>? PartIds { get; init; }
    public string? JobTitle { get; init; }
    public List<int>? AddressIds { get; init; }
    public List<int>? LevelIds { get; init; }
    public List<int>? StatusIds { get; init; }
    public DateOnly? LeftStartDate { get; init; }
    public DateOnly? RightStartDate { get; init; }
    public DateOnly? LeftClosingDate { get; init; }
    public DateOnly? RightClosingDate { get; init; }
}


public class GetJobDetailsQueryHandler : IRequestHandler<GetJobDetailsQuery, PaginatedList<JobDetailDto>>
{
    private readonly IApplicationDbContext _context;

    public GetJobDetailsQueryHandler(IApplicationDbContext context)
    {
        _context = context;
    }

    public async Task<PaginatedList<JobDetailDto>> Handle(GetJobDetailsQuery request, CancellationToken cancellationToken)
    {
        var query = _context.JobDetails
        .Include(jd => jd.Part)
        .Include(jd => jd.Job)
        .Include(jd => jd.JobDetailAddresses)
            .ThenInclude(jda => jda.Address)
        .Include(jd => jd.Level)
        .Include(jd => jd.Status)
        .Include(jd => jd.WorkingForm)
        .Where(jd => jd.IsDeleted == false)
        .AsQueryable();

        if (request.PartIds != null && request.PartIds.Any())
        {
            query = query.Where(jd => jd.PartId.HasValue && request.PartIds.Contains(jd.PartId.Value));
        }

        if (request.JobTitle != null)
        {
            var searchText = request.JobTitle.ToLower();
            query = query.Where(c => c.JobTitle != null && c.JobTitle.ToLower().Contains(searchText));
        }

        if (request.AddressIds != null && request.AddressIds.Any())
        {
            query = query.Where(jd => jd.JobDetailAddresses.Any(jda => request.AddressIds.Contains(jda.AddressId)));
        }

        if (request.LevelIds != null && request.LevelIds.Any())
        {
            query = query.Where(jd => jd.LevelId.HasValue && request.LevelIds.Contains(jd.LevelId.Value));
        }

        if (request.StatusIds != null && request.StatusIds.Any())
        {
            query = query.Where(jd => jd.StatusId.HasValue && request.StatusIds.Contains(jd.StatusId.Value));
        }

        if (request.LeftStartDate.HasValue)
        {
            query = query.Where(jd => jd.StartDate >= request.LeftStartDate.Value);
        }
        if (request.RightStartDate.HasValue)
        {
            query = query.Where(jd => jd.StartDate <= request.RightStartDate.Value);
        }

        if (request.LeftClosingDate.HasValue)
        {
            query = query.Where(jd => jd.ClosingDate >= request.LeftClosingDate.Value);
        }
        if (request.RightClosingDate.HasValue)
        {
            query = query.Where(jd => jd.ClosingDate <= request.RightClosingDate.Value);
        }

        var totalCount = await query.CountAsync(cancellationToken);

        var items = await query
            .Select(jd => new JobDetailDto
            {
                Id = jd.Id,
                JobTitle = jd.JobTitle,
                Part = jd.Part,
                Job = jd.Job,
                JobDetailAddresses = jd.JobDetailAddresses,
                Level = jd.Level,
                Quantity = jd.Quantity,
                Status = jd.Status,
                WorkingForm = jd.WorkingForm,
                StartDate = jd.StartDate,
                ClosingDate = jd.ClosingDate,
                DesiredTime = jd.DesiredTime,
                RecruitmentPlanId = jd.RecruitmentPlanId,
                CandidateCount = _context.RecruitmentStageDetails
                    .Where(rsd => rsd.RecruitmentStage != null
                        && rsd.RecruitmentStage.Name == "Onboarding"
                        && rsd.Candidate != null
                        && rsd.Candidate.JobDetailId == jd.Id)
                    .Count()
            })
            .OrderByDescending(jd => jd.Id)
            .Skip((request.PageNumber - 1) * request.PageSize)
            .Take(request.PageSize)
            .ToListAsync(cancellationToken);

        return new PaginatedList<JobDetailDto>(items, totalCount, request.PageNumber, request.PageSize);
    }
}
