using CVAPIService.Application.JobDetails.Commands.CreateJobDetail;
using CVAPIService.Domain.Entities;
namespace CVAPIService.Application.JobDetails.Queries.GetByIdJobDetail;

public class JobDetailDto
{
    public int Id { get; set; }
    public string? JobTitle { get; set; } = string.Empty;
    public int? PartId { get; set; }
    public int? LevelId { get; set; }
    public DateOnly? StartDate { get; set; }
    public int? StatusId { get; set; }
    public string? JDFileName { get; set; }
    public int? WorkingFormId { get; set; }
    public int? JobId { get; set; }
    public List<int>? AddressIds { get; set; } = new();
    public int? Quantity { get; set; }
    public DateOnly? ClosingDate { get; set; }
    public string? Description { get; set; }
    public DateOnly? DesiredTime { get; set; }
    public int? RecruitmentPlanId { get; set; }
    public List<AttributeDefinitionDto> AttributeDefinitions { get; set; } = new();
    public List<JobDetailAttributeDto> Attributes { get; set; } = new();

    private class Mapping : Profile
    {
        public Mapping()
        {
            CreateMap<JobDetail, JobDetailDto>()
                .ForMember(d => d.Attributes, opt => opt.MapFrom(s => s.JobDetailAttributes.Select(ja =>
                    new JobDetailAttributeDto
                    {
                        Name = ja.AttributeDefinition!.Name,
                        Value = ja.Value
                    })))
                .ForMember(d => d.AttributeDefinitions, opt => opt.MapFrom(s => s.JobDetailAttributes
                    .Select(ja => ja.AttributeDefinition)
                    .Distinct()))
                .ForMember(dest => dest.AddressIds, opt =>
                    opt.MapFrom(src => src.JobDetailAddresses.Select(ja => ja.AddressId)));
        }
    }
}
