using CVAPIService.Domain.Entities;
namespace CVAPIService.Application.JobDetails.Queries.GetByIdJobDetail;
public class AttributeDefinitionDto
{
    public string Name { get; set; } = string.Empty;
    public string DataType { get; set; } = string.Empty;

    private class Mapping : Profile
    {
        public Mapping()
        {
            CreateMap<AttributeDefinition, AttributeDefinitionDto>();
        }
    }
}
