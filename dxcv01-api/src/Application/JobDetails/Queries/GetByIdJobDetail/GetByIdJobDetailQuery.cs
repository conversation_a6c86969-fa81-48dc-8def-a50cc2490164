using CVAPIService.Application.Common.Interfaces;
using CVAPIService.Domain.Entities;
namespace CVAPIService.Application.JobDetails.Queries.GetByIdJobDetail;

public record GetByIdJobDetailQuery(int Id) : IRequest<JobDetailDto>;

public class GetByIdJobDetailQueryHandler : IRequestHandler<GetByIdJobDetailQuery, JobDetailDto>
{
    private readonly IApplicationDbContext _context;
    private readonly IMapper _mapper;

    public GetByIdJobDetailQueryHandler(IApplicationDbContext context, IMapper mapper)
    {
        _context = context;
        _mapper = mapper;
    }

    public async Task<JobDetailDto> Handle(GetByIdJobDetailQuery request, CancellationToken cancellationToken)
    {
        var jobDetail = await _context.JobDetails
            .Include(x => x.JobDetailAttributes)
                .ThenInclude(x => x.AttributeDefinition)
            .Include(x => x.JobDetailAddresses)
                .ThenInclude(x => x.Address)
            .AsNoTracking()
            .FirstOrDefaultAsync(x => x.Id == request.Id, cancellationToken);

        if (jobDetail == null)
        {
            throw new NotFoundException(nameof(JobDetail), request.Id.ToString());
        }

        return _mapper.Map<JobDetailDto>(jobDetail);
    }
}
