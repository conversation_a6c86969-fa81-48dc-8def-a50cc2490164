﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using CVAPIService.Application.Common.Interfaces;
using CVAPIService.Domain.Entities;

namespace CVAPIService.Application.Levels.Queries.GetLevels;
public record GetLevelsQuery : IRequest<List<Level>>;

public class GetLevelsQueryHandler : IRequestHandler<GetLevelsQuery, List<Level>>
{
    private readonly IApplicationDbContext _context;

    public GetLevelsQueryHandler(IApplicationDbContext context)
    {
        _context = context;
    }

    public async Task<List<Level>> Handle(GetLevelsQuery request, CancellationToken cancellationToken)
    {
        return await _context.Levels.ToListAsync(cancellationToken);
    }
}

