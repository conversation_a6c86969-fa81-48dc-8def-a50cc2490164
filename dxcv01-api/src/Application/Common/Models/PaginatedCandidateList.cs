﻿using CVAPIService.Application.Candidates.Queries.GetCandidates;

namespace CVAPIService.Application.Common.Models;

public class PaginatedCandidateList<T>
{
    public IReadOnlyCollection<T> Items { get; }
    public int PageNumber { get; }
    public int TotalPages { get; }
    public int Count { get; }
    public int TotalCount { get; }
    public int PageSize { get; }
    public IReadOnlyCollection<RecruitmentStageDto> CandidateStageCounts { get; }

    public PaginatedCandidateList(IReadOnlyCollection<T> items, int count, int totalCount, int pageNumber, int pageSize, IReadOnlyCollection<RecruitmentStageDto> candidateStageCounts)
    {
        PageNumber = pageNumber;
        TotalPages = (int)Math.Ceiling(count / (double)pageSize);
        Count = count;
        TotalCount = totalCount;
        PageSize = pageSize;
        Items = items;
        CandidateStageCounts = candidateStageCounts;
    }

    public bool HasPreviousPage => PageNumber > 1;

    public bool HasNextPage => PageNumber < TotalPages;

    public static async Task<PaginatedCandidateList<T>> CreateAsync(IQueryable<T> source, int pageNumber, int pageSize, List<RecruitmentStageDto> candidateStageCounts)
    {
        var count = await source.CountAsync();
        var totalCount = await source.CountAsync();
        var items = await source.Skip((pageNumber - 1) * pageSize).Take(pageSize).ToListAsync();

        return new PaginatedCandidateList<T>(items, count, totalCount, pageNumber, pageSize, candidateStageCounts);
    }
}
