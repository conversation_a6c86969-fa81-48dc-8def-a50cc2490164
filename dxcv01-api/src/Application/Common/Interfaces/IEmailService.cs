﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace CVAPIService.Application.Common.Interfaces;
public interface IEmailService
{
    Task SendEmailAsync(string fromEmail, string fromEmailPassword, string fromName, List<string>? email, string subject, string htmlMessage, List<string>? ccEmails = null, string? statusSendMail = "");
}
