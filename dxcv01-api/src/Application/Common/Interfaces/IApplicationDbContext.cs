﻿using CVAPIService.Domain.Entities;

namespace CVAPIService.Application.Common.Interfaces;

public interface IApplicationDbContext
{
    Task<int> SaveChangesAsync(CancellationToken cancellationToken);
    DbSet<Address> Addresses { get; }
    DbSet<Candidate> Candidates { get; }
    DbSet<CVSource> CVSources { get; }
    DbSet<Job> Jobs { get; }
    DbSet<JobDetail> JobDetails { get; }
    DbSet<Level> Levels { get; }
    DbSet<Part> Parts { get; }
    DbSet<RecruitmentStage> RecruitmentStages { get; }
    DbSet<RecruitmentStageDetail> RecruitmentStageDetails { get; }
    DbSet<Status> Statuses { get; }
    DbSet<WorkingForm> WorkingForms { get; }
    DbSet<AttributeDefinition> AttributeDefinitions { get; }
    DbSet<CandidateAttribute> CandidateAttributes { get; }
    DbSet<JobDetailAttribute> JobDetailAttributes { get; }
    DbSet<JobDetailAddress> JobDetailAddresses { get; }
    DbSet<FileStorage> FileStorages { get; }
}
