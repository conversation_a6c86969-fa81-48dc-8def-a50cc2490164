using System.Text;
using System.Text.RegularExpressions;

namespace CVAPIService.Application.Common.Helpers;

public static class FileHelper
{
    public static string NormalizeFileName(string fileName)
    {
        if (string.IsNullOrWhiteSpace(fileName)) return "";

        return Regex.Replace(fileName.Trim().ToLowerInvariant(), @"\s+", "")
                    .Normalize(NormalizationForm.FormC);
    }
}
