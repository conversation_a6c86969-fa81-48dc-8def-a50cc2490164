﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using CVAPIService.Application.Common.Interfaces;
using CVAPIService.Domain.Entities;

namespace CVAPIService.Application.CVSources.Queries.GetCVSources;
public record GetCVSourcesQuery : IRequest<List<CVSource>>;

public class GetCVSourcesQueryHandler : IRequestHandler<GetCVSourcesQuery, List<CVSource>>
{
    private readonly IApplicationDbContext _context;

    public GetCVSourcesQueryHandler(IApplicationDbContext context)
    {
        _context = context;
    }

    public async Task<List<CVSource>> Handle(GetCVSourcesQuery request, CancellationToken cancellationToken)
    {
        return await _context.CVSources.ToListAsync(cancellationToken);
    }
}

