﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using CVAPIService.Application.Common.Interfaces;
using CVAPIService.Domain.Entities;

namespace CVAPIService.Application.Parts.Queries.GetParts;
public record GetPartsQuery : IRequest<List<Part>>;

public class GetPartsQueryHandler : IRequestHandler<GetPartsQuery, List<Part>>
{
    private readonly IApplicationDbContext _context;

    public GetPartsQueryHandler(IApplicationDbContext context)
    {
        _context = context;
    }

    public async Task<List<Part>> Handle(GetPartsQuery request, CancellationToken cancellationToken)
    {
        return await _context.Parts.ToListAsync(cancellationToken);
    }
}

