﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using CVAPIService.Application.Common.Interfaces;
using CVAPIService.Domain.Entities;

namespace CVAPIService.Application.WorkingForms.Queries.GetWorkingForms;
public record GetWorkingFormsQuery : IRequest<List<WorkingForm>>;

public class GetWorkingFormsQueryHandler : IRequestHandler<GetWorkingFormsQuery, List<WorkingForm>>
{
    private readonly IApplicationDbContext _context;

    public GetWorkingFormsQueryHandler(IApplicationDbContext context)
    {
        _context = context;
    }

    public async Task<List<WorkingForm>> Handle(GetWorkingFormsQuery request, CancellationToken cancellationToken)
    {
        return await _context.WorkingForms.ToListAsync(cancellationToken);
    }
}

