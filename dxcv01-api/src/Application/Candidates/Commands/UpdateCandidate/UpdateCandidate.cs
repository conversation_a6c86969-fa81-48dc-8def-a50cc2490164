using AutoMapper;
using CVAPIService.Application.Candidates.Commands.CreateCandidate;
using CVAPIService.Application.Common.Interfaces;
using CVAPIService.Application.JobDetails.Commands.CreateJobDetail;
using CVAPIService.Domain.Entities;
using MediatR;
using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
namespace CVAPIService.Application.Candidates.Commands.UpdateCandidate;
public record UpdateCandidateCommand : IRequest<int>
{
    public required int Id { get; set; }
    public required string Name { get; set; }
    public required string Email { get; set; }
    public required string Phone { get; set; }
    public int? YearOfBirth { get; set; }
    public required string Address { get; set; }
    public bool Sex { get; set; }
    public string? PlaceOfTraining { get; set; }
    public int? GraduateYear { get; set; }
    public string? Major { get; set; }
    public string? Ranking { get; set; }
    public string? CVFileName { get; set; }
    public DateTime? ApplicationPeriod { get; set; } = DateTime.UtcNow;
    public int JobDetailId { get; set; }
    public int CVSourceId { get; set; }
    public List<AttributeDefinitionDto>? AttributeDefinitions { get; set; } = new();
    public List<CandidateAttributeDto>? Attributes { get; set; } = new();
    public List<RecruitmentStageDetailDto>? RecruitmentStageDetails { get; set; } = new();

    private class Mapping : Profile
    {
        public Mapping()
        {
            CreateMap<UpdateCandidateCommand, Candidate>()
                .ForMember(d => d.RecruitmentStageDetails, opt => opt.Ignore())
                .ForMember(d => d.ApplicationPeriod, opt => opt.MapFrom(s => s.ApplicationPeriod.HasValue ? DateTime.SpecifyKind(s.ApplicationPeriod.Value, DateTimeKind.Utc) : (DateTime?)null));
        }
    }
}

public class UpdateCandidateCommandHandler : IRequestHandler<UpdateCandidateCommand, int>
{
    private readonly IApplicationDbContext _context;
    private readonly IMapper _mapper;

    public UpdateCandidateCommandHandler(IApplicationDbContext context, IMapper mapper)
    {
        _context = context;
        _mapper = mapper;
    }

    public async Task<int> Handle(UpdateCandidateCommand request, CancellationToken cancellationToken)
    {
        var entity = await GetAndValidateCandidate(request.Id, cancellationToken);
        UpdateCandidateProperties(entity, request);

        var attributeDefinitionIds = await ProcessAttributeDefinitions(request, cancellationToken);
        await UpdateCandidateAttributes(request, entity.Id, attributeDefinitionIds, cancellationToken);
        await UpdateRecruitmentStageDetails(request, entity.Id, cancellationToken);

        await _context.SaveChangesAsync(cancellationToken);
        return entity.Id;
    }

    private async Task<Candidate> GetAndValidateCandidate(int id, CancellationToken cancellationToken)
    {
        var entity = await _context.Candidates.FindAsync(new object[] { id }, cancellationToken);
        if (entity == null)
        {
            throw new NotFoundException(nameof(Candidate), id.ToString());
        }
        return entity;
    }
    private void UpdateCandidateProperties(Candidate entity, UpdateCandidateCommand request)
    {
        _mapper.Map(request, entity);
    }
    private async Task<Dictionary<string, int>> ProcessAttributeDefinitions(UpdateCandidateCommand request, CancellationToken cancellationToken)
    {
        var attributeDefinitionIds = new Dictionary<string, int>();

        foreach (var attrDef in request.AttributeDefinitions ?? new List<AttributeDefinitionDto>())
        {
            attributeDefinitionIds[attrDef.Name] = await UpdateAttributeDefinition(attrDef, cancellationToken);
        }

        return attributeDefinitionIds;
    }
    private async Task<int> UpdateAttributeDefinition(AttributeDefinitionDto attrDef, CancellationToken cancellationToken)
    {
        var existingAttrDef = await _context.AttributeDefinitions
            .FirstOrDefaultAsync(ad => ad.Name == attrDef.Name && ad.DataType == attrDef.DataType, cancellationToken);

        if (existingAttrDef == null)
        {
            var attributeDefinition = _mapper.Map<AttributeDefinition>(attrDef);
            await _context.AttributeDefinitions.AddAsync(attributeDefinition, cancellationToken);
            await _context.SaveChangesAsync(cancellationToken);
            return attributeDefinition.Id;
        }

        return existingAttrDef.Id;
    }

    private async Task UpdateCandidateAttributes(UpdateCandidateCommand request, int candidateId, Dictionary<string, int> attributeDefinitionIds, CancellationToken cancellationToken)
    {
        var existingAttributes = await _context.CandidateAttributes
            .Where(jda => jda.CandidateId == candidateId)
            .ToListAsync(cancellationToken);

        _context.CandidateAttributes.RemoveRange(existingAttributes);
        await _context.SaveChangesAsync(cancellationToken);

        foreach (var attr in request.Attributes ?? new List<CandidateAttributeDto>())
        {
            var candidateAttr = new CandidateAttribute
            {
                CandidateId = candidateId,
                AttributeDefinitionId = attributeDefinitionIds[attr.Name],
                Value = attr.Value
            };
            _context.CandidateAttributes.Add(candidateAttr);
        }
    }
    private async Task UpdateRecruitmentStageDetails(UpdateCandidateCommand request, int candidateId, CancellationToken cancellationToken)
    {
        var existingStageDetails = await _context.RecruitmentStageDetails
            .Where(x => x.CandidateId == candidateId)
            .ToListAsync(cancellationToken);

        _context.RecruitmentStageDetails.RemoveRange(existingStageDetails);
        await _context.SaveChangesAsync(cancellationToken);

        foreach (var detail in request.RecruitmentStageDetails ?? new List<RecruitmentStageDetailDto>())
        {
            var recruitmentStageDetail = new RecruitmentStageDetail
            {
                CandidateId = candidateId,
                RecruitmentStageId = detail.RecruitmentStageId,
                Date = detail.Date,
                Note = detail.Note
            };
            _context.RecruitmentStageDetails.Add(recruitmentStageDetail);
        }
    }
}
