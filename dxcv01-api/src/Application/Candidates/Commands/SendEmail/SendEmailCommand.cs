﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using CVAPIService.Application.Common.Interfaces;

namespace CVAPIService.Application.Candidates.Commands.SendEmail;
public record SendEmailCommand : IRequest
{

    public string fromEmail { get; init; } = "";
    public string fromName { get; init; } = "";
    public string fromPassword { get; init; } = "";
    public List<string>? email { get; init; } = null;
    public string subject { get; init; } = "";
    public string htmlMessage { get; init; } = "";
    public List<string>? ccEmails { get; init; } = null;
    public string? statusSendMail { get; init; } = "";
}

public class SendMailCommandHandler : IRequestHandler<SendEmailCommand>
{
    private readonly IEmailService _emailService;
    public SendMailCommandHandler(IEmailService emailService)
    {
        _emailService = emailService;
    }
    public async Task Handle(SendEmailCommand request, CancellationToken cancellationToken)
    {
        await _emailService.SendEmailAsync(request.fromEmail, request.fromPassword, request.fromName, request.email, request.subject, request.htmlMessage, request.ccEmails, request.statusSendMail);
    }
}

