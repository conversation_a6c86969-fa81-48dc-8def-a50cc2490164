using CVAPIService.Application.Candidates.Commands.CreateCandidate;
using CVAPIService.Application.Common.Interfaces;
using FluentValidation;

namespace CVAPIService.Application.Candidates.Commands.CreateCandidateList;

public class CreateCandidateListResult
{
    public List<string> ImportSuccessfully { get; set; } = new();
    public List<string> ImportFailed { get; set; } = new();
}

public record CreateCandidateListCommand : IRequest<CreateCandidateListResult>
{
    public required List<CreateCandidateCommand> Candidates { get; set; }
}

public class CreateCandidateListCommandHandler : IRequestHandler<CreateCandidateListCommand, CreateCandidateListResult>
{
    private readonly IMediator _mediator;
    private readonly IMapper _mapper;
    private readonly CreateCandidateCommandValidator _validator;

    public CreateCandidateListCommandHandler(
        IMediator mediator, 
        IMapper mapper,
        IApplicationDbContext context)
    {
        _mediator = mediator;
        _mapper = mapper;
        _validator = new CreateCandidateCommandValidator(context);
    }

    public async Task<CreateCandidateListResult> Handle(CreateCandidateListCommand request, CancellationToken cancellationToken)
    {
        var result = new CreateCandidateListResult();

        foreach (var candidateDto in request.Candidates)
        {
            var validationResult = await _validator.ValidateAsync(candidateDto, cancellationToken);
            
            if (!validationResult.IsValid)
            {
                result.ImportFailed.Add($"{candidateDto.Name}: {string.Join(", ", validationResult.Errors.Select(e => e.ErrorMessage))}");
                continue;
            }

            try
            {
                var createCommand = _mapper.Map<CreateCandidateCommand>(candidateDto);
                var id = await _mediator.Send(createCommand, cancellationToken);
                result.ImportSuccessfully.Add(candidateDto.Name);
            }
            catch (Exception ex)
            {
                result.ImportFailed.Add($"{candidateDto.Name}: {ex.Message}");
            }
        }
        
        return result;
    }
}
