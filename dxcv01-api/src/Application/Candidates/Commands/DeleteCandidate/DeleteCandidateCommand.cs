﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using CVAPIService.Application.Common.Interfaces;

namespace CVAPIService.Application.Candidates.Commands.DeleteCandidate;

public record DeleteCandidateCommand(int Id) : IRequest;

public class DeleteCandidateCommandHandler : IRequestHandler<DeleteCandidateCommand>
{
    private readonly IApplicationDbContext _context;

    public DeleteCandidateCommandHandler(IApplicationDbContext context)
    {
        _context = context;
    }

    public async Task Handle(DeleteCandidateCommand request, CancellationToken cancellationToken)
    {
        var Candidate = await _context.Candidates.FirstOrDefaultAsync(x => x.Id == request.Id);
        if (Candidate != null)
        {
            Candidate.IsDeleted = true;
        }
        if (!string.IsNullOrEmpty(Candidate!.CVFileName))
        {
            var fileInfo = await _context.FileStorages
                .FirstOrDefaultAsync(f => f.CvFileName == Candidate.CVFileName, cancellationToken);
            
            if (fileInfo != null)
            {
                _context.FileStorages.Remove(fileInfo);
            }   
        }
        await _context.SaveChangesAsync(cancellationToken);
    }
}
