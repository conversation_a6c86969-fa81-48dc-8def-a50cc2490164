﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using CVAPIService.Application.Common.Interfaces;

namespace CVAPIService.Application.Candidates.Commands.DeleteCandidate;

public record DeleteCandidatesCommand : IRequest<Unit>
{
    public List<int>? Ids { get; init; }
}

public class DeleteCandidatesCommandHandler : IRequestHandler<DeleteCandidatesCommand, Unit>
{
    private readonly IApplicationDbContext _context;

    public DeleteCandidatesCommandHandler(IApplicationDbContext context)
    {
        _context = context;
    }

    public async Task<Unit> Handle(DeleteCandidatesCommand request, CancellationToken cancellationToken)
    {
        if(request != null && request.Ids != null)
        {
            foreach (var id in request.Ids)
            {
                var Candidate = await _context.Candidates.FirstOrDefaultAsync(x => x.Id == id);
                if (Candidate != null)
                {
                    Candidate.IsDeleted = true;
                }

                if (!string.IsNullOrEmpty(Candidate!.CVFileName))
                {
                    var fileInfo = await _context.FileStorages
                        .FirstOrDefaultAsync(f => f.CvFileName == Candidate.CVFileName, cancellationToken);
                        
                    if (fileInfo != null)
                    {
                        _context.FileStorages.Remove(fileInfo);
                    }                  
                }
            }
            await _context.SaveChangesAsync(cancellationToken);
        }
        return Unit.Value;
    }
}
