using CVAPIService.Application.Candidates.Commands.CreateCandidate;
using CVAPIService.Application.Common.Interfaces;
using CVAPIService.Application.JobDetails.Commands.CreateJobDetail;
using FluentValidation;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace CVAPIService.Application.Candidates.Commands.CreateCandidate
{
    public class CreateCandidateCommandValidator : AbstractValidator<CreateCandidateCommand>
    {
        private readonly IApplicationDbContext _context;

        public CreateCandidateCommandValidator(IApplicationDbContext context)
        {
            _context = context;

            RuleFor(x => x.Name)
                .NotEmpty().WithMessage("Name is required")
                .MaximumLength(200).WithMessage("Name cannot exceed 200 characters");

            RuleFor(x => x.Email)
                .NotEmpty().WithMessage("Email is required")
                .EmailAddress().WithMessage("Email is not in the correct format");

            RuleFor(x => x.Phone)
                .NotEmpty().WithMessage("Phone number is required")
                .Matches(@"^\d{10,11}$").WithMessage("Phone number must have 10-11 digits");

            RuleFor(x => x.Address)
                .NotEmpty().WithMessage("Address is required")
                .MaximumLength(500).WithMessage("Address cannot exceed 500 characters");

            RuleFor(x => x.Sex)
                .NotNull().WithMessage("Sex is required");

            RuleFor(x => x.JobDetailId)
                .NotEmpty().WithMessage("JobDetail is required")
                .GreaterThan(0).WithMessage("JobDetail is not valid")
                .MustAsync(BeExistingJobDetail).WithMessage("JobDetail does not exist");

            RuleFor(x => x.CVSourceId)
                .GreaterThan(0).WithMessage("CVSource is not valid")
                .MustAsync(BeExistingCVSource).WithMessage("CVSource does not exist");
        }

        private async Task<bool> BeExistingJobDetail(int jobDetailId, CancellationToken cancellationToken)
        {
            return await _context.JobDetails
                .AnyAsync(x => x.Id == jobDetailId, cancellationToken);
        }

        private async Task<bool> BeExistingCVSource(int cvSourceId, CancellationToken cancellationToken)
        {
            return await _context.CVSources
                .AnyAsync(x => x.Id == cvSourceId, cancellationToken);
        }
    }
}
