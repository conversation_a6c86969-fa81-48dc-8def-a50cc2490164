using AutoMapper;
using CVAPIService.Application.Common.Helpers;
using CVAPIService.Application.Common.Interfaces;
using CVAPIService.Application.JobDetails.Commands.CreateJobDetail;
using CVAPIService.Domain.Entities;
using MediatR;
using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
namespace CVAPIService.Application.Candidates.Commands.CreateCandidate;
public record CreateCandidateCommand : IRequest<int>
{
    public required string Name { get; set; }
    public required string Email { get; set; }
    public required string Phone { get; set; }
    public int? YearOfBirth { get; set; }
    public required string Address { get; set; }
    public bool Sex { get; set; }
    public string? PlaceOfTraining { get; set; }
    public int? GraduateYear { get; set; }
    public string? Major { get; set; }
    public string? Ranking { get; set; }
    public string? CVFileName { get; set; }
    public DateTime? ApplicationPeriod { get; set; } = DateTime.UtcNow;
    public int JobDetailId { get; set; }
    public int CVSourceId { get; set; }
    public List<AttributeDefinitionDto>? AttributeDefinitions { get; set; } = new();
    public List<CandidateAttributeDto>? Attributes { get; set; } = new();
    public List<RecruitmentStageDetailDto>? RecruitmentStageDetails { get; set; } = new();
    private class Mapping : Profile
    {
        public Mapping()
        {
            CreateMap<CreateCandidateCommand, Candidate>()
                .ForMember(d => d.Id, opt => opt.Ignore())
                .ForMember(d => d.ApplicationPeriod, opt => opt.MapFrom(s => s.ApplicationPeriod.HasValue ? DateTime.SpecifyKind(s.ApplicationPeriod.Value, DateTimeKind.Utc) : (DateTime?)null));
        }
    }
}

public record CandidateAttributeDto
{
    public required string Name { get; set; }
    public required string Value { get; set; }
}

public record RecruitmentStageDetailDto
{
    public int RecruitmentStageId { get; set; }
    public DateOnly? Date { get; set; }
    public string? Note { get; set; }

    private class Mapping : Profile
    {
        public Mapping()
        {
            CreateMap<RecruitmentStageDetailDto, RecruitmentStageDetail>()
                .ForMember(dest => dest.Id, opt => opt.Ignore());
        }
    }
}

public class CreateCandidateCommandHandler : IRequestHandler<CreateCandidateCommand, int>
{
    private readonly IApplicationDbContext _context;
    private readonly IMapper _mapper;

    public CreateCandidateCommandHandler(IApplicationDbContext context, IMapper mapper)
    {
        _context = context;
        _mapper = mapper;
    }

    public async Task<int> Handle(CreateCandidateCommand request, CancellationToken cancellationToken)
    {
        request.CVFileName = FileHelper.NormalizeFileName(request.CVFileName ?? "");
        var entity = await CreateCandidateEntity(request, cancellationToken);
        await _context.SaveChangesAsync(cancellationToken);

        var attributeDefinitionIds = await ProcessAttributeDefinitions(request, cancellationToken);
        await AddCandidateAttributes(request, entity.Id, attributeDefinitionIds, cancellationToken);

        return entity.Id;
    }

    private async Task<Candidate> CreateCandidateEntity(CreateCandidateCommand request, CancellationToken cancellationToken)
    {
        var entity = _mapper.Map<Candidate>(request);
        await _context.Candidates.AddAsync(entity, cancellationToken);
        return entity;
    }

    private async Task<Dictionary<string, int>> ProcessAttributeDefinitions(CreateCandidateCommand request, CancellationToken cancellationToken)
    {
        var attributeDefinitionIds = new Dictionary<string, int>();

        foreach (var attrDef in request.AttributeDefinitions ?? new List<AttributeDefinitionDto>())
        {
            attributeDefinitionIds[attrDef.Name] = await AddAttributeDefinition(attrDef, cancellationToken);
        }

        return attributeDefinitionIds;
    }

    private async Task<int> AddAttributeDefinition(AttributeDefinitionDto attrDef, CancellationToken cancellationToken)
    {
        var existingAttrDef = await _context.AttributeDefinitions
            .FirstOrDefaultAsync(ad => ad.Name == attrDef.Name && ad.DataType == attrDef.DataType, cancellationToken);

        if (existingAttrDef == null)
        {
            var attributeDefinition = new AttributeDefinition
            {
                Name = attrDef.Name,
                DataType = attrDef.DataType
            };
            _context.AttributeDefinitions.Add(attributeDefinition);
            await _context.SaveChangesAsync(cancellationToken);
            return attributeDefinition.Id;
        }

        return existingAttrDef.Id;
    }

    private async Task AddCandidateAttributes(CreateCandidateCommand request, int candidateId, Dictionary<string, int> attributeDefinitionIds, CancellationToken cancellationToken)
    {
        foreach (var attr in request.Attributes ?? new List<CandidateAttributeDto>())
        {
            var candidateAttr = new CandidateAttribute
            {
                CandidateId = candidateId,
                AttributeDefinitionId = attributeDefinitionIds[attr.Name],
                Value = attr.Value
            };
            _context.CandidateAttributes.Add(candidateAttr);
        }
        await _context.SaveChangesAsync(cancellationToken);
    }
}
