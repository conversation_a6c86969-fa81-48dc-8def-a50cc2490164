using AutoMapper;
using CVAPIService.Application.Candidates.Commands.CreateCandidate;
using CVAPIService.Domain.Entities;
using System;
using System.Collections.Generic;
namespace CVAPIService.Application.Candidates.Queries.GetByIdCandidate;
public class CandidateDto
{
    public int Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Email { get; set; } = string.Empty;
    public string Phone { get; set; } = string.Empty;
    public int? YearOfBirth { get; set; }
    public string Address { get; set; } = string.Empty;
    public bool Sex { get; set; }
    public string? PlaceOfTraining { get; set; }
    public int GraduateYear { get; set; }
    public string? Major { get; set; }
    public string? Ranking { get; set; }
    public string? CVFileName { get; set; }
    public string? UniqueFileName { get; set; }
    public DateTime? ApplicationPeriod { get; set; }
    public int JobDetailId { get; set; }
    public int CVSourceId { get; set; }
    public List<AttributeDefinitionDto> AttributeDefinitions { get; set; } = new();
    public List<CandidateAttributeDto> Attributes { get; set; } = new();
    public List<RecruitmentStageDetailDto> RecruitmentStageDetails { get; set; } = new();

    private class Mapping : Profile
    {
        public Mapping()
        {
            CreateMap<Candidate, CandidateDto>()
                .ForMember(d => d.Attributes, opt => opt.MapFrom(s => s.CandidateAttributes.Select(ca => 
                    new CandidateAttributeDto 
                    { 
                        Name = ca.AttributeDefinition!.Name,
                        Value = ca.Value 
                    })))
                .ForMember(d => d.AttributeDefinitions, opt => opt.MapFrom(s => s.CandidateAttributes
                    .Select(ca => ca.AttributeDefinition)
                    .Distinct()))
                .ForMember(d => d.RecruitmentStageDetails, opt => opt.MapFrom(s => s.RecruitmentStageDetails))
                .ForMember(d => d.RecruitmentStageDetails, opt => opt.MapFrom(s => s.RecruitmentStageDetails.OrderBy(rsd => rsd.Id)));
        }
    }
}
