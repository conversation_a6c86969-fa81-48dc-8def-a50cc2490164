using System.Text;
using System.Text.RegularExpressions;
using CVAPIService.Application.Common.Helpers;
using CVAPIService.Application.Common.Interfaces;
using CVAPIService.Domain.Entities;
namespace CVAPIService.Application.Candidates.Queries.GetByIdCandidate;
public record GetByIdCandidateQuery(int Id) : IRequest<CandidateDto>;

public class GetByIdCandidateQueryHandler : IRequestHandler<GetByIdCandidateQuery, CandidateDto>
{
    private readonly IApplicationDbContext _context;
    private readonly IMapper _mapper;

    public GetByIdCandidateQueryHandler(IApplicationDbContext context, IMapper mapper)
    {
        _context = context;
        _mapper = mapper;
    }

    public async Task<CandidateDto> Handle(GetByIdCandidateQuery request, CancellationToken cancellationToken)
    {
        var candidate = await _context.Candidates
            .Include(x => x.CandidateAttributes)
                .ThenInclude(x => x.AttributeDefinition)
            .Include(x => x.RecruitmentStageDetails)
            .AsNoTracking()
            .FirstOrDefaultAsync(x => x.Id == request.Id, cancellationToken);

        if (candidate == null)
        {
            throw new NotFoundException(nameof(Candidate), request.Id.ToString());
        }

        var candidateDto = _mapper.Map<CandidateDto>(candidate);

        if (!string.IsNullOrEmpty(candidate.CVFileName))
        {
            var normalizedCandidateFileName = FileHelper.NormalizeFileName(candidate.CVFileName);

            var fileInfo = _context.FileStorages
                .Where(f => f.CvFileName != null)
                .AsEnumerable()
                .Where(f => FileHelper.NormalizeFileName(f.CvFileName) == normalizedCandidateFileName)
                .OrderByDescending(f => f.Id)
                .FirstOrDefault();

            if (fileInfo != null)
            {
                candidateDto.UniqueFileName = fileInfo.UniqueFileName;
            }
        }
        return candidateDto;
    }
}
