using CVAPIService.Domain.Entities;
namespace CVAPIService.Application.Candidates.Queries.GetByIdCandidate;
public class RecruitmentStageDetailDto
{
    public int Id { get; set; }
    public int CandidateId { get; set; }
    public int RecruitmentStageId { get; set; }
    public DateOnly? Date { get; set; }
    public string? Note { get; set; }

    private class Mapping : Profile
    {
        public Mapping()
        {
            CreateMap<RecruitmentStageDetail, RecruitmentStageDetailDto>();
        }
    }
}
