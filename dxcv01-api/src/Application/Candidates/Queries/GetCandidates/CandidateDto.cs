﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using CVAPIService.Domain.Entities;

namespace CVAPIService.Application.Candidates.Queries.GetCandidates;
public class CandidateDto
{
    public int Id { get; set; }
    public required string Name { get; set; }
    public required string Email { get; set; }
    public required string Phone { get; set; }
    public int? YearOfBirth { get; set; }
    public required string Address { get; set; }
    public bool Sex { get; set; }
    public string? PlaceOfTraining { get; set; }
    public int GraduateYear { get; set; }
    public string? Major { get; set; }
    public string? Ranking { get; set; }
    public string? CVFileName { get; set; }
    public string? UniqueFileName { get; set; }
    public DateTime? ApplicationPeriod { get; set; }
    public int JobDetailId { get; set; }
    public JobDetail? JobDetail { get; set; }
    public int CVSourceId { get; set; }
    public CVSource? CVSource { get; set; }
    public ICollection<RecruitmentStageDetail> RecruitmentStageDetails { get; set; } = new List<RecruitmentStageDetail>();
    public ICollection<CandidateAttribute> CandidateAttributes { get; set; } = new List<CandidateAttribute>();
    public bool IsDeleted { get; set; } = false;
    public RecruitmentStageDto? RecruitmentStage { get; set; }
}
