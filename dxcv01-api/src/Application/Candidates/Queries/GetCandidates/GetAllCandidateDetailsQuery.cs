﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using CVAPIService.Application.Common.Interfaces;
using CVAPIService.Application.Common.Models;
using CVAPIService.Domain.Entities;
using Microsoft.EntityFrameworkCore;

namespace CVAPIService.Application.Candidates.Queries.GetCandidates;

public record GetAllCandidatesQuery : IRequest<List<Candidate>>;


public class GetAllCandidatesQueryHandler : IRequestHandler<GetAllCandidatesQuery, List<Candidate>>
{
    private readonly IApplicationDbContext _context;

    public GetAllCandidatesQueryHandler(IApplicationDbContext context)
    {
        _context = context;
    }

    public async Task<List<Candidate>> Handle(GetAllCandidatesQuery request, CancellationToken cancellationToken)
    {
        return await _context.Candidates
            .Include(c => c.CandidateAttributes)
                .ThenInclude(ca => ca.AttributeDefinition)
            .Include(c => c.JobDetail)
                .ThenInclude(jd => jd!.Job)
            .Include(c => c.JobDetail)
                .ThenInclude(jd => jd!.JobDetailAddresses)
                    .ThenInclude(jda => jda.Address)
            .Include(c => c.JobDetail)
                .ThenInclude(jd => jd!.Part)
            .Include(c => c!.CVSource)
            .Include(c => c.RecruitmentStageDetails)
                .ThenInclude(rs => rs!.RecruitmentStage)
            .Where(c => c.IsDeleted == false)
            .ToListAsync(cancellationToken);
    }
}
