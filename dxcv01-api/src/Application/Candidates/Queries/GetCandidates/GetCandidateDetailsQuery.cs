﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using CVAPIService.Application.Common.Helpers;
using CVAPIService.Application.Common.Interfaces;
using CVAPIService.Application.Common.Models;
using CVAPIService.Domain.Entities;
using Microsoft.EntityFrameworkCore;

namespace CVAPIService.Application.Candidates.Queries.GetCandidates;

public record GetCandidatesQuery : IRequest<PaginatedCandidateList<CandidateDto>>
{
    public string? Text { get; set; } = "";
    public int PageNumber { get; init; } = 1;
    public int PageSize { get; init; } = 10;
    public List<int>? PartIds { get; init; }
    public string? JobTitle { get; init; }
    public List<int>? AddressIds { get; init; }
    public bool? IsBirthDayAsc { get; init; } = true;
    public bool? IsApplyAsc { get; init; } = true;
    public string? yearFrom { get; init; }
    public string? yearTo { get; init;}
    // public int? schoolId { get; init; }
    public DateTime? startDate { get; init; }
    public DateTime? endDate { get; init; }
    public int? RecruitmentStageId { get; init; }
    public string? StatusSendMail { get; init; }
    public int? RecruitmentPlanId { get; init; }
}


public class GetCandidatesQueryHandler : IRequestHandler<GetCandidatesQuery, PaginatedCandidateList<CandidateDto>>
{
    private readonly IApplicationDbContext _context;

    public GetCandidatesQueryHandler(IApplicationDbContext context)
    {
        _context = context;
    }

    public async Task<PaginatedCandidateList<CandidateDto>> Handle(GetCandidatesQuery request, CancellationToken cancellationToken)
    {
        var query = _context.Candidates
            .Include(c => c.CandidateAttributes)
                .ThenInclude(ca => ca.AttributeDefinition)
            .Include(c => c.JobDetail)
                .ThenInclude(jd => jd!.Job)
            .Include(c => c.JobDetail)
                .ThenInclude(jd => jd!.JobDetailAddresses)
                    .ThenInclude(jda => jda.Address)
            .Include(c => c.JobDetail)
                .ThenInclude(jd => jd!.Part)
            .Include(c => c!.CVSource)
            .Include(c => c.RecruitmentStageDetails)
                .ThenInclude(rs => rs!.RecruitmentStage)
            .Where(c => c.IsDeleted == false)
            .AsQueryable();

        if (!string.IsNullOrEmpty(request.Text))
        {
            var searchText = request.Text.ToLower();

            query = query.Where(c =>
                c.Name.ToLower().Contains(searchText) ||
                c.Email.ToLower().Contains(searchText) ||
                c.Phone.ToLower().Contains(searchText) ||
                c.CandidateAttributes.Any(ca => ca.Value.ToLower().Contains(searchText)) ||
                (c.CVFileName != null && _context.FileStorages
                    .Any(fs => fs.CvFileName == c.CVFileName &&
                               fs.ToTextCV != null &&
                               fs.ToTextCV.ToLower().Contains(searchText)))
            );
        }

        if (request.PartIds != null && request.PartIds.Any())
        {
            query = query.Where(c => c.JobDetail != null && c.JobDetail.PartId.HasValue && request.PartIds.Contains(c.JobDetail.PartId.Value));
        }

        if (request.JobTitle != null)
        {
            var searchText = request.JobTitle.ToLower();
            query = query.Where(c => c.JobDetail!.JobTitle != null && c.JobDetail!.JobTitle.ToLower().Contains(searchText));
        }

        if (request.AddressIds != null && request.AddressIds.Any())
        {
            query = query.Where(c => c.JobDetail != null &&
                c.JobDetail.JobDetailAddresses.Any(jda => request.AddressIds.Contains(jda.AddressId)));
        }

        if (!string.IsNullOrEmpty(request.yearFrom))
        {
            if (int.TryParse(request.yearFrom, out int yearFromValue))
            {
                query = query.Where(c => c.YearOfBirth >= yearFromValue);
            }
        }

        if (!string.IsNullOrEmpty(request.yearTo))
        {
            if (int.TryParse(request.yearTo, out int yearToValue))
            {
                query = query.Where(c => c.YearOfBirth <= yearToValue);
            }
        }

        if (request.startDate != null && request.startDate.HasValue)
        {
            query = query.Where(c => c.ApplicationPeriod >= request.startDate.Value);
        }

        if (request.endDate != null && request.endDate.HasValue)
        {
            query = query.Where(c => c.ApplicationPeriod <= request.endDate.Value.AddDays(1));
        }

        if (request.IsApplyAsc.HasValue && request.IsBirthDayAsc.HasValue)
        {
            if (request.IsApplyAsc.Value && request.IsBirthDayAsc.Value)
            {
                query = query.OrderBy(c => c.ApplicationPeriod.HasValue ? c.ApplicationPeriod.Value.Date : DateTime.MinValue)
                             .ThenBy(c => c.YearOfBirth)
                             .ThenByDescending(c => c.Id);
            }
            else if (request.IsApplyAsc.Value && !request.IsBirthDayAsc.Value)
            {
                query = query.OrderBy(c => c.ApplicationPeriod.HasValue ? c.ApplicationPeriod.Value.Date : DateTime.MinValue)
                             .ThenByDescending(c => c.YearOfBirth)
                             .ThenByDescending(c => c.Id);
            }
            else if (!request.IsApplyAsc.Value && request.IsBirthDayAsc.Value)
            {
                query = query.OrderByDescending(c => c.ApplicationPeriod.HasValue ? c.ApplicationPeriod.Value.Date : DateTime.MinValue)
                             .ThenBy(c => c.YearOfBirth)
                             .ThenByDescending(c => c.Id);
            }
            else if (!request.IsApplyAsc.Value && !request.IsBirthDayAsc.Value)
            {
                query = query.OrderByDescending(c => c.ApplicationPeriod.HasValue ? c.ApplicationPeriod.Value.Date : DateTime.MinValue)
                             .ThenByDescending(c => c.YearOfBirth)
                             .ThenByDescending(c => c.Id);
            }
        }

        if (!string.IsNullOrEmpty(request.StatusSendMail))
        {
            query = query.Where(c => c.statusSendMail == request.StatusSendMail);
        }

        // Filter by RecruitmentPlanId - lọc ứng viên theo JobDetail có RecruitmentPlanId
        if (request.RecruitmentPlanId.HasValue && request.RecruitmentPlanId.Value > 0)
        {
            query = query.Where(c => c.JobDetail != null && c.JobDetail.RecruitmentPlanId == request.RecruitmentPlanId.Value);
        }

        // Filter by RecruitmentStageId - Apply this filter before getting counts
        if (request.RecruitmentStageId.HasValue && request.RecruitmentStageId != 0)
        {
            query = query.Where(c => c.RecruitmentStageDetails.Any() &&
                c.RecruitmentStageDetails
                    .OrderByDescending(rsd => rsd.Date)
                    .ThenByDescending(rsd => rsd.Id)
                    .FirstOrDefault()!.RecruitmentStageId == request.RecruitmentStageId.Value);
        }

        // Get all items for candidate stage counts (before pagination)
        var allitems = await query
            .ToListAsync(cancellationToken);

        var totalCount = allitems.Count;

        // Apply pagination
        var candidates = allitems
            .Skip((request.PageNumber - 1) * request.PageSize)
            .Take(request.PageSize)
            .ToList();

        var items = candidates.Select(candidate => new CandidateDto
        {
            Id = candidate.Id,
            Name = candidate.Name,
            Email = candidate.Email,
            Phone = candidate.Phone,
            YearOfBirth = candidate.YearOfBirth,
            Address = candidate.Address,
            Sex = candidate.Sex,
            PlaceOfTraining = candidate.PlaceOfTraining,
            GraduateYear = candidate.GraduateYear,
            Major = candidate.Major,
            Ranking = candidate.Ranking,
            CVFileName = candidate.CVFileName,
            UniqueFileName = null,
            ApplicationPeriod = candidate.ApplicationPeriod,
            JobDetailId = candidate.JobDetailId,
            JobDetail = candidate.JobDetail,
            CVSourceId = candidate.CVSourceId,
            CVSource = candidate.CVSource,
            RecruitmentStageDetails = candidate.RecruitmentStageDetails,
            CandidateAttributes = candidate.CandidateAttributes,
            IsDeleted = candidate.IsDeleted,
            RecruitmentStage = candidate.RecruitmentStageDetails.Any() ? new RecruitmentStageDto
            {
                Id = candidate.RecruitmentStageDetails.OrderByDescending(d => d.Id).FirstOrDefault()?.RecruitmentStageId ?? 0
            } : null
        }).ToList();

        foreach (var candidateDto in items)
        {
            if (!string.IsNullOrEmpty(candidateDto.CVFileName))
            {
                var normalizedCandidateFileName = FileHelper.NormalizeFileName(candidateDto.CVFileName);

                var fileInfo = _context.FileStorages
                    .Where(f => f.CvFileName != null)
                    .AsEnumerable()
                    .Where(f => FileHelper.NormalizeFileName(f.CvFileName) == normalizedCandidateFileName)
                    .OrderByDescending(f => f.Id)
                    .FirstOrDefault();

                if (fileInfo != null)
                {
                    candidateDto.UniqueFileName = fileInfo.UniqueFileName;
                }
            }
        }

        var candidateStageCounts = allitems
            .Where(c => c.RecruitmentStageDetails.Any())
            .Select(c => new
            {
                CandidateId = c.Id,
                RecruitmentStageId = c.RecruitmentStageDetails
                    .OrderByDescending(rsd => rsd.Date)
                    .ThenByDescending(rsd => rsd.Id)
                    .FirstOrDefault() != null
                    ? c.RecruitmentStageDetails
                        .OrderByDescending(rsd => rsd.Date)
                        .ThenByDescending(rsd => rsd.Id)
                        .FirstOrDefault()!.RecruitmentStageId
                    : (int?)null
            })
            .GroupBy(c => c.RecruitmentStageId)
            .Select(g => new RecruitmentStageDto
            {
                Id = g.Key ?? 0,
                CandidateCount = g.Count()
            })
            .ToList();

        var count = candidates.Count();

        return new PaginatedCandidateList<CandidateDto>(items, count, totalCount, request.PageNumber, request.PageSize, candidateStageCounts);
    }
}
