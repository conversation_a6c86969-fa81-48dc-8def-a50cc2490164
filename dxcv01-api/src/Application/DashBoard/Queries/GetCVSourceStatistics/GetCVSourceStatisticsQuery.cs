using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using CVAPIService.Application.Common.Interfaces;
using MediatR;
using Microsoft.EntityFrameworkCore;

namespace Application.DashBoard.Queries.GetCVSourceStatistics
{
    public record GetCVSourceStatisticsQuery(int? PartId = null, DateOnly? StartDate = null, DateOnly? ClosingDate = null, int[]? AddressIds = null) : IRequest<Dictionary<string, int>>;

    public class GetCVSourceStatisticsQueryHandler : IRequestHandler<GetCVSourceStatisticsQuery, Dictionary<string, int>>
    {
        private readonly IApplicationDbContext _context;

        public GetCVSourceStatisticsQueryHandler(IApplicationDbContext context)
        {
            _context = context;
        }

        public async Task<Dictionary<string, int>> Handle(GetCVSourceStatisticsQuery request, CancellationToken cancellationToken)
        {
            var query = _context.JobDetails
                .Include(j => j.Candidates)
                .ThenInclude(c => c.CVSource)
                .Include(j => j.JobDetailAddresses)
                .Where(jc => !jc.IsDeleted)
                .AsQueryable();

            if (request.PartId.HasValue)
            {
                query = query.Where(jd => jd.PartId == request.PartId.Value);
            }

            if (request.StartDate.HasValue)
            {
                query = query.Where(jd => jd.StartDate >= request.StartDate.Value);
            }

            if (request.ClosingDate.HasValue)
            {
                query = query.Where(jd => jd.ClosingDate <= request.ClosingDate.Value);
            }

            if (request.AddressIds != null && request.AddressIds.Any())
            {
                query = query.Where(j => j.JobDetailAddresses.Any(ja => request.AddressIds.Contains(ja.AddressId)));
            }

            var jobDetails = await query.ToListAsync(cancellationToken);

            var candidates = jobDetails
                .SelectMany(jd => jd.Candidates)
                .Where(c => !c.IsDeleted)
                .ToList();

            var sourceStats = candidates
                .GroupBy(c => c.CVSource)
                .OrderByDescending(g => g.Count())
                .ToDictionary(
                    g => g.Key!.Name,
                    g => g.Count()
                );

            var allSources = await _context.CVSources
                .Select(s => s.Name)
                .ToArrayAsync(cancellationToken);

            foreach (var source in allSources)
            {
                if (!sourceStats.ContainsKey(source))
                {
                    sourceStats[source] = 0;
                }
            }

            return sourceStats;
        }
    }
}
