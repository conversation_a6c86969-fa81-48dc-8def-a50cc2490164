﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using CVAPIService.Application.Candidates.Queries.GetCandidates;
using CVAPIService.Application.Common.Interfaces;
using CVAPIService.Domain.Entities;

namespace CVAPIService.Application.DashBoard.Queries.GetAverageRecruitmentTime
{
    public record GetAverageRecruitmentTimeQuery: IRequest<List<AverageRecruitmentTimeDto>>
    {
        public List<int>? LevelIds { get; set; }
        public List<int>? JobIds { get; set; }
    }

    public class GetAverageRecruitmentTimeQueryHandler : IRequestHandler<GetAverageRecruitmentTimeQuery, List<AverageRecruitmentTimeDto>>
    {
        private readonly IApplicationDbContext _context;

        public GetAverageRecruitmentTimeQueryHandler(IApplicationDbContext context)
        {
            _context = context;
        }

        public async Task<List<AverageRecruitmentTimeDto>> Handle(GetAverageRecruitmentTimeQuery request, CancellationToken cancellationToken)
        {
            var query = _context.JobDetails
                .Include(jd => jd.Candidates)
                    .ThenInclude(jdrs => jdrs.RecruitmentStageDetails)
                .Include(jd => jd.Status)
                .Include(jd => jd.Level)
                .AsQueryable();

            if (request.JobIds != null && request.JobIds.Any())
            {
                var jobTitles = await _context.JobDetails
                    .Where(jd => request.JobIds.Contains(jd.Id))
                    .Select(jd => jd.JobTitle)
                    .Distinct()
                    .ToListAsync(cancellationToken);

                query = query.Where(jd => jobTitles.Contains(jd.JobTitle));
            }

            if (request.LevelIds != null && request.LevelIds.Any())
            {
                query = query.Where(jd => jd.LevelId.HasValue && request.LevelIds.Contains(jd.LevelId.Value));
            }

            query = query.Where(jd => jd.Status != null &&
                             (jd.Status.Name == "Done" ||
                              jd.Status.Name == "Doing" ||
                              jd.Status.Name == "Cancel" ||
                              jd.Status.Name == "Pause"));

            var jobDetails = await query
                .Select(jd => new
                {
                    jd.JobTitle,
                    jd.Level,
                    jd.Status,
                    jd.StartDate,
                    jd.ClosingDate,
                    jd.Quantity
                })
                .ToListAsync(cancellationToken);

            var result = jobDetails
                .GroupBy(jd => new { jd.JobTitle, Level = jd.Level?.Name })
                .Select(group => new AverageRecruitmentTimeDto
                {
                    JobTitle = group.Key.JobTitle,
                    JobLevel = group.Key.Level,
                    AverageRecruitmentTime = Math.Round(
                        group.Average(jd =>
                            jd.Status != null && jd.Status.Name == "Done"
                                ? (jd.ClosingDate.HasValue && jd.StartDate.HasValue && jd.Quantity.HasValue && jd.Quantity.Value > 0)
                                    ? (double)(jd.ClosingDate.Value.DayNumber - jd.StartDate.Value.DayNumber) / (double)jd.Quantity.Value
                                    : 0.0
                                : jd.Status != null && jd.Status.Name == "Doing" && jd.StartDate.HasValue
                                    ? (double)(DateOnly.FromDateTime(DateTime.Now).DayNumber - jd.StartDate.Value.DayNumber)
                                    : jd.Status != null && (jd.Status.Name == "Cancel" || jd.Status.Name == "Pause") && jd.ClosingDate.HasValue && jd.StartDate.HasValue
                                        ? (double)(jd.ClosingDate.Value.DayNumber - jd.StartDate.Value.DayNumber)
                                        : 0.0
                        ), 2)
                })
                .OrderBy(x => x.JobTitle)
                .ThenBy(x => x.JobLevel)
                .ToList();

            return result ?? new List<AverageRecruitmentTimeDto>();
        }
    }
}
