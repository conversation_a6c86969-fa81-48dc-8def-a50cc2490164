﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace CVAPIService.Application.DashBoard.Queries.GetAverageRecruitmentTime
{
    public class AverageRecruitmentTimeDto
    {
        public int Id { get; set; }
        public string? JobTitle { get; set; }
        public string? JobLevel { get; set; }
        public double? AverageRecruitmentTime { get; set; }
    }
}
