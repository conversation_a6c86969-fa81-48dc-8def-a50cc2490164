using AutoMapper;
using CVAPIService.Application.Common.Interfaces;
using CVAPIService.Application.DashBoard.Queries.GetRecruitmentEfficiencyStatistics;
using Microsoft.EntityFrameworkCore;

public record GetRecruitmentEfficiencyStatisticsQuery(int? PartId = null, DateOnly? StartDate = null, DateOnly? ClosingDate = null, int[]? AddressIds = null) : IRequest<List<RecruitmentEfficiencyDto>>;

public class GetRecruitmentEfficiencyStatisticsQueryHandler : IRequestHandler<GetRecruitmentEfficiencyStatisticsQuery, List<RecruitmentEfficiencyDto>>
{
    private readonly IApplicationDbContext _context;

    public GetRecruitmentEfficiencyStatisticsQueryHandler(IApplicationDbContext context, IMapper mapper)
    {
        _context = context;
    }

    public async Task<List<RecruitmentEfficiencyDto>> Handle(GetRecruitmentEfficiencyStatisticsQuery request, CancellationToken cancellationToken)
    {
        var jobDetailsQuery = _context.JobDetails
            .Include(j => j.Candidates)
            .ThenInclude(j => j.RecruitmentStageDetails)
            .ThenInclude(j => j.RecruitmentStage)
            .Include(j => j.JobDetailAddresses)
            .Where(j => !j.IsDeleted)
            .AsQueryable();

        if (request.PartId.HasValue)
        {
            jobDetailsQuery = jobDetailsQuery.Where(jd => jd.PartId.HasValue && jd.PartId.Value == request.PartId.Value);
        }

        if (request.StartDate.HasValue)
        {
            jobDetailsQuery = jobDetailsQuery.Where(jd => jd.StartDate >= request.StartDate.Value);
        }

        if (request.ClosingDate.HasValue)
        {
            jobDetailsQuery = jobDetailsQuery.Where(jd => jd.ClosingDate <= request.ClosingDate.Value);
        }

        if (request.AddressIds != null && request.AddressIds.Any())
        {
            jobDetailsQuery = jobDetailsQuery.Where(j => j.JobDetailAddresses.Any(ja => request.AddressIds.Contains(ja.AddressId)));
        }

        var jobDetails = await jobDetailsQuery.ToListAsync(cancellationToken);

        if (!jobDetails.Any())
        {
            return new List<RecruitmentEfficiencyDto>();
        }

        var result = jobDetails.Select(jd => new RecruitmentEfficiencyDto
        {
            Id = jd.Id,
            JobTitle = $"{jd.Id} - {jd.JobTitle}",
            Candidate = jd.Candidates.Where(c => !c.IsDeleted).SelectMany(c => c.RecruitmentStageDetails).Count(rsd => rsd.RecruitmentStage != null && rsd.RecruitmentStage.Name == "Candidate"),
            Applied = jd.Candidates.Where(c => !c.IsDeleted).SelectMany(c => c.RecruitmentStageDetails).Count(rsd => rsd.RecruitmentStage != null && rsd.RecruitmentStage.Name == "Applied"),
            PassScreening = jd.Candidates.Where(c => !c.IsDeleted).SelectMany(c => c.RecruitmentStageDetails).Count(rsd => rsd.RecruitmentStage != null && rsd.RecruitmentStage.Name == "Pass Screening"),
            Interview = jd.Candidates.Where(c => !c.IsDeleted).SelectMany(c => c.RecruitmentStageDetails).Count(rsd => rsd.RecruitmentStage != null && rsd.RecruitmentStage.Name == "Interview"),
            Offered = jd.Candidates.Where(c => !c.IsDeleted).SelectMany(c => c.RecruitmentStageDetails).Count(rsd => rsd.RecruitmentStage != null && rsd.RecruitmentStage.Name == "Offered"),
            Onboarding = jd.Candidates.Where(c => !c.IsDeleted).SelectMany(c => c.RecruitmentStageDetails).Count(rsd => rsd.RecruitmentStage != null && rsd.RecruitmentStage.Name == "Onboarding"),
            RejectOffer = jd.Candidates.Where(c => !c.IsDeleted).SelectMany(c => c.RecruitmentStageDetails).Count(rsd => rsd.RecruitmentStage != null && rsd.RecruitmentStage.Name == "Reject Offer"),
            NotQualified = jd.Candidates.Where(c => !c.IsDeleted).SelectMany(c => c.RecruitmentStageDetails).Count(rsd => rsd.RecruitmentStage != null && rsd.RecruitmentStage.Name == "CV Fail")
        }).ToList();

        foreach (var item in result)
        {
            var jobDetail = jobDetails.FirstOrDefault(jd => jd.Id == item.Id);
            var quantity = jobDetail?.Quantity ?? 0;

            item.ResponseRate = quantity > 0
                ? $"{item.Onboarding}/{quantity}"
                : "0/0";
        }

        var allStats = new RecruitmentEfficiencyDto
        {
            Id = result.Max(r => r.Id) + 1,
            JobTitle = "ALL",
            Candidate = result.Sum(r => r.Candidate),
            Applied = result.Sum(r => r.Applied),
            PassScreening = result.Sum(r => r.PassScreening),
            Interview = result.Sum(r => r.Interview),
            Offered = result.Sum(r => r.Offered),
            Onboarding = result.Sum(r => r.Onboarding),
            RejectOffer = result.Sum(r => r.RejectOffer),
            NotQualified = result.Sum(r => r.NotQualified)
        };

        var totalQuantity = jobDetails.Sum(jd => jd.Quantity);

        allStats.ResponseRate = totalQuantity > 0
            ? $"{allStats.Onboarding}/{totalQuantity}"
            : "0/0";

        result.Add(allStats);

        return result;
    }
}
