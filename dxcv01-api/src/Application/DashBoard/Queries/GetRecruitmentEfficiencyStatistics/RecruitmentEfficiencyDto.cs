namespace CVAPIService.Application.DashBoard.Queries.GetRecruitmentEfficiencyStatistics;

public class RecruitmentEfficiencyDto
{
    public int Id { get; set; }
    public string JobTitle { get; set; } = string.Empty;
    public string ResponseRate { get; set; } = string.Empty;
    public int Candidate { get; set; }
    public int Applied { get; set; }
    public int PassScreening { get; set; }
    public int Interview { get; set; }
    public int Offered { get; set; }
    public int Onboarding { get; set; }
    public int RejectOffer { get; set; }
    public int NotQualified { get; set; }
}
