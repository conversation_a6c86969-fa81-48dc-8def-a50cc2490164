using CVAPIService.Application.DashBoard.Queries.GetMonthlyRecruitmentStatistics;
using CVAPIService.Domain.Entities;

public static class MonthlyStatisticsExtensions
{
    private static readonly Dictionary<int, string> MonthProperties = new()
    {
        { 1, nameof(MonthlyStatisticsDto.Jan) },
        { 2, nameof(MonthlyStatisticsDto.Feb) },
        { 3, nameof(MonthlyStatisticsDto.Mar) },
        { 4, nameof(MonthlyStatisticsDto.Apr) },
        { 5, nameof(MonthlyStatisticsDto.May) },
        { 6, nameof(MonthlyStatisticsDto.Jun) },
        { 7, nameof(MonthlyStatisticsDto.Jul) },
        { 8, nameof(MonthlyStatisticsDto.Aug) },
        { 9, nameof(MonthlyStatisticsDto.Sep) },
        { 10, nameof(MonthlyStatisticsDto.Oct) },
        { 11, nameof(MonthlyStatisticsDto.Nov) },
        { 12, nameof(MonthlyStatisticsDto.Dec) }
    };

    public static MonthlyStatisticsDto FillMonthlyQuantities(this MonthlyStatisticsDto dto, List<JobDetail> jobDetails)
    {
        foreach (var month in MonthProperties.Keys)
        {
            var quantity = jobDetails
                .Where(j => j.DesiredTime.HasValue && j.DesiredTime.Value.Month == month)
                .Sum(j => j.Quantity);

            typeof(MonthlyStatisticsDto).GetProperty(MonthProperties[month])!
                .SetValue(dto, quantity);
        }
        return dto;
    }

    public static MonthlyStatisticsDto FillMonthlyStageCount(this MonthlyStatisticsDto dto,
        List<JobDetail> jobDetails, int year, string stageName)
    {
        foreach (var month in MonthProperties.Keys)
        {
            var count = jobDetails
                .SelectMany(j => j.Candidates)
                .Where(c => !c.IsDeleted)
                .SelectMany(c => c.RecruitmentStageDetails)
                .Count(r => r.RecruitmentStage?.Name == stageName
                    && r.Date.HasValue
                    && r.Date.Value.Month == month
                    && r.Date.Value.Year == year);

            typeof(MonthlyStatisticsDto).GetProperty(MonthProperties[month])!
                .SetValue(dto, count);
        }
        return dto;
    }

    public static MonthlyStatisticsDto FillMonthlyPerformance(this MonthlyStatisticsDto dto,
        MonthlyStatisticsDto required, MonthlyStatisticsDto onboarding)
    {
        foreach (var month in MonthProperties.Keys)
        {
            var requiredValue = (int)typeof(MonthlyStatisticsDto)
                .GetProperty(MonthProperties[month])!
                .GetValue(required)!;

            var onboardingValue = (int)typeof(MonthlyStatisticsDto)
                .GetProperty(MonthProperties[month])!
                .GetValue(onboarding)!;

            var performance = requiredValue == 0 ? 0 :
                (int)Math.Round((double)onboardingValue / requiredValue * 100);

            typeof(MonthlyStatisticsDto).GetProperty(MonthProperties[month])!
                .SetValue(dto, performance);
        }
        return dto;
    }
}
