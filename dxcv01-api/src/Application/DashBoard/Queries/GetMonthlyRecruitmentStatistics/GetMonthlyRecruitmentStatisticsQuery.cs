using CVAPIService.Application.Common.Interfaces;
using CVAPIService.Application.DashBoard.Queries.GetMonthlyRecruitmentStatistics;
using CVAPIService.Domain.Entities;

public record GetMonthlyRecruitmentStatisticsQuery(int? Year = null)
    : IRequest<List<MonthlyStatisticsDto>>;
public class GetMonthlyRecruitmentStatisticsQueryHandler
    : IRequestHandler<GetMonthlyRecruitmentStatisticsQuery, List<MonthlyStatisticsDto>>
{
    private readonly IApplicationDbContext _context;

    public GetMonthlyRecruitmentStatisticsQueryHandler(IApplicationDbContext context)
    {
        _context = context;
    }

    public async Task<List<MonthlyStatisticsDto>> Handle(GetMonthlyRecruitmentStatisticsQuery request, CancellationToken cancellationToken)
    {
        var currentYear = request.Year ?? DateTime.Now.Year;

        var query = _context.JobDetails
            .Include(j => j.Candidates)
            .ThenInclude(c => c.RecruitmentStageDetails)
            .ThenInclude(r => r.RecruitmentStage)
            .Where(j => j.DesiredTime!.Value.Year == currentYear && !j.<PERSON>);

        var jobDetails = await query.ToListAsync(cancellationToken);

        if (!jobDetails.Any())
        {
            return new List<MonthlyStatisticsDto>();
        }

        var result = new List<MonthlyStatisticsDto>();
        var currentId = 1;

        result.Add(CreateQuantityRequiredStats(currentId++, jobDetails));

        result.Add(CreateMonthlyStats(currentId++, "Applied", jobDetails, currentYear));
        result.Add(CreateMonthlyStats(currentId++, "Interview", jobDetails, currentYear));
        result.Add(CreateMonthlyStats(currentId++, "Offer", jobDetails, currentYear));
        result.Add(CreateMonthlyStats(currentId++, "Onboarding", jobDetails, currentYear));

        var quantityRequired = result[0];
        var onboarding = result.Last();
        result.Add(CreatePerformanceStats(currentId++, quantityRequired, onboarding));

        return result;
    }

    private MonthlyStatisticsDto CreateQuantityRequiredStats(int id, List<JobDetail> jobDetails)
    {
        return new MonthlyStatisticsDto
        {
            Id = id,
            NameRecruitment = "Quantity required"
        }.FillMonthlyQuantities(jobDetails);
    }

    private MonthlyStatisticsDto CreateMonthlyStats(int id, string name, List<JobDetail> jobDetails, int year)
    {
        return new MonthlyStatisticsDto
        {
            Id = id,
            NameRecruitment = name
        }.FillMonthlyStageCount(jobDetails, year, name);
    }

    private MonthlyStatisticsDto CreatePerformanceStats(int id, MonthlyStatisticsDto required, MonthlyStatisticsDto onboarding)
    {
        return new MonthlyStatisticsDto
        {
            Id = id,
            NameRecruitment = "% Monthly Recruitment Performance"
        }.FillMonthlyPerformance(required, onboarding);
    }
}
