using CVAPIService.Application.Common.Interfaces;
using CVAPIService.Application.DashBoard.Queries.GetRecruitmentOverviewStatistics;
using CVAPIService.Domain.Entities;
using MediatR;

public record GetRecruitmentOverviewStatisticsQuery(DateOnly? StartDate = null, DateOnly? ClosingDate = null, int[]? AddressIds = null)
    : IRequest<RecruitmentOverviewDto>;

public class GetRecruitmentOverviewStatisticsQueryHandler
    : IRequestHandler<GetRecruitmentOverviewStatisticsQuery, RecruitmentOverviewDto>
{
    private readonly IApplicationDbContext _context;

    public GetRecruitmentOverviewStatisticsQueryHandler(IApplicationDbContext context)
    {
        _context = context;
    }

    public async Task<RecruitmentOverviewDto> Handle(GetRecruitmentOverviewStatisticsQuery request,
        CancellationToken cancellationToken)
    {
        var query = _context.JobDetails
            .Include(j => j.Candidates)
            .ThenInclude(c => c.RecruitmentStageDetails)
            .ThenInclude(r => r.RecruitmentStage)
            .Include(j => j.JobDetailAddresses)
            .Include(j => j.Status)
            .Where(j => !j.IsDeleted)
            .AsQueryable();

        if (request.AddressIds != null && request.AddressIds.Any())
        {
            query = query.Where(j => j.JobDetailAddresses.Any(ja => request.AddressIds.Contains(ja.AddressId)));
        }

        if (request.StartDate.HasValue)
        {
            query = query.Where(jd => jd.ClosingDate >= request.StartDate.Value);
        }

        if (request.ClosingDate.HasValue)
        {
            query = query.Where(jd => jd.StartDate <= request.ClosingDate!.Value);
        }

        var jobDetails = await query.ToListAsync(cancellationToken);
        if (!jobDetails.Any())
        {
            return new RecruitmentOverviewDto();
        }
        var doneStatusId = await _context.Statuses
            .Where(s => s.Name == "Done")
            .Select(s => s.Id)
            .FirstOrDefaultAsync(cancellationToken);

        var completedJobs = jobDetails
                    .Where(j => j.StatusId == doneStatusId
                        && (request.StartDate == null || j.DoneTime >= request.StartDate)
                        && (request.ClosingDate == null || j.DoneTime <= request.ClosingDate))
                    .ToList();

        return new RecruitmentOverviewDto
        {
            NumberOfJobsRequested = jobDetails.Count,
            NumberOfJobsRecruited = completedJobs.Count,
            NumberOfStaffsRequested = jobDetails.Sum(j => j.Quantity ?? 0),
            NumberOfStaffsRecruited = CountCandidatesInStage(jobDetails, "Onboarding", request),
            Applied = CountCandidatesInStage(jobDetails, "Applied", request),
            Interview = CountCandidatesInStage(jobDetails, "Interview", request),
            Offer = CountCandidatesInStage(jobDetails, "Offered", request),
            RejectOffer = CountCandidatesInStage(jobDetails, "Reject Offer", request)
        };
    }

    private int CountCandidatesInStage(List<JobDetail> jobDetails, string stageName, GetRecruitmentOverviewStatisticsQuery request)
    {
        return jobDetails.SelectMany(j => j.Candidates)
            .Where(c => !c.IsDeleted)
            .SelectMany(c => c.RecruitmentStageDetails)
            .Count(r => r.RecruitmentStage != null
                && r.RecruitmentStage.Name == stageName
                && (!request.StartDate.HasValue || r.Date >= request.StartDate)
                && (!request.ClosingDate.HasValue || r.Date <= request.ClosingDate));
    }
}
