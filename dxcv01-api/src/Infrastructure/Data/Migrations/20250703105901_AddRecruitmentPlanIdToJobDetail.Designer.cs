﻿// <auto-generated />
using System;
using CVAPIService.Infrastructure.Data;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;

#nullable disable

namespace CVAPIService.Infrastructure.Data.Migrations
{
    [DbContext(typeof(ApplicationDbContext))]
    [Migration("20250703105901_AddRecruitmentPlanIdToJobDetail")]
    partial class AddRecruitmentPlanIdToJobDetail
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "8.0.8")
                .HasAnnotation("Relational:MaxIdentifierLength", 63);

            NpgsqlModelBuilderExtensions.UseIdentityByDefaultColumns(modelBuilder);

            modelBuilder.Entity("CVAPIService.Domain.Entities.Address", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.ToTable("Addresses");
                });

            modelBuilder.Entity("CVAPIService.Domain.Entities.AttributeDefinition", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("DataType")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.ToTable("AttributeDefinitions");
                });

            modelBuilder.Entity("CVAPIService.Domain.Entities.CVSource", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.ToTable("CVSources");
                });

            modelBuilder.Entity("CVAPIService.Domain.Entities.Candidate", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("Address")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime?>("ApplicationPeriod")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CVFileName")
                        .HasColumnType("text");

                    b.Property<int>("CVSourceId")
                        .HasColumnType("integer");

                    b.Property<string>("Email")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int>("GraduateYear")
                        .HasColumnType("integer");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<int>("JobDetailId")
                        .HasColumnType("integer");

                    b.Property<string>("Major")
                        .HasColumnType("text");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("Phone")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("PlaceOfTraining")
                        .HasColumnType("text");

                    b.Property<string>("Ranking")
                        .HasColumnType("text");

                    b.Property<bool>("Sex")
                        .HasColumnType("boolean");

                    b.Property<int?>("YearOfBirth")
                        .HasColumnType("integer");

                    b.Property<string>("statusSendMail")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("CVSourceId");

                    b.HasIndex("JobDetailId");

                    b.ToTable("Candidates");
                });

            modelBuilder.Entity("CVAPIService.Domain.Entities.CandidateAttribute", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<int>("AttributeDefinitionId")
                        .HasColumnType("integer");

                    b.Property<int>("CandidateId")
                        .HasColumnType("integer");

                    b.Property<string>("Value")
                        .IsRequired()
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("AttributeDefinitionId");

                    b.HasIndex("CandidateId");

                    b.ToTable("CandidateAttributes");
                });

            modelBuilder.Entity("CVAPIService.Domain.Entities.FileStorage", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("CvFileName")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("ToTextCV")
                        .HasColumnType("text");

                    b.Property<string>("UniqueFileName")
                        .IsRequired()
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.ToTable("FileStorages");
                });

            modelBuilder.Entity("CVAPIService.Domain.Entities.Job", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.ToTable("Jobs");
                });

            modelBuilder.Entity("CVAPIService.Domain.Entities.JobDetail", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<int?>("AddressId")
                        .HasColumnType("integer");

                    b.Property<DateOnly?>("ClosingDate")
                        .HasColumnType("date");

                    b.Property<string>("Description")
                        .HasColumnType("text");

                    b.Property<DateOnly?>("DesiredTime")
                        .HasColumnType("date");

                    b.Property<DateOnly?>("DoneTime")
                        .HasColumnType("date");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<string>("JDFileName")
                        .HasColumnType("text");

                    b.Property<int?>("JobId")
                        .HasColumnType("integer");

                    b.Property<string>("JobTitle")
                        .HasColumnType("text");

                    b.Property<int?>("LevelId")
                        .HasColumnType("integer");

                    b.Property<int?>("PartId")
                        .HasColumnType("integer");

                    b.Property<int?>("Quantity")
                        .HasColumnType("integer");

                    b.Property<int?>("RecruitmentPlanId")
                        .HasColumnType("integer");

                    b.Property<DateOnly?>("StartDate")
                        .HasColumnType("date");

                    b.Property<int?>("StatusId")
                        .HasColumnType("integer");

                    b.Property<int?>("WorkingFormId")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("AddressId");

                    b.HasIndex("JobId");

                    b.HasIndex("LevelId");

                    b.HasIndex("PartId");

                    b.HasIndex("StatusId");

                    b.HasIndex("WorkingFormId");

                    b.ToTable("JobDetails");
                });

            modelBuilder.Entity("CVAPIService.Domain.Entities.JobDetailAddress", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<int>("AddressId")
                        .HasColumnType("integer");

                    b.Property<int>("JobDetailId")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("AddressId");

                    b.HasIndex("JobDetailId");

                    b.ToTable("JobDetailAddresses");
                });

            modelBuilder.Entity("CVAPIService.Domain.Entities.JobDetailAttribute", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<int>("AttributeDefinitionId")
                        .HasColumnType("integer");

                    b.Property<int>("JobDetailId")
                        .HasColumnType("integer");

                    b.Property<string>("Value")
                        .IsRequired()
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("AttributeDefinitionId");

                    b.HasIndex("JobDetailId");

                    b.ToTable("JobDetailAttributes");
                });

            modelBuilder.Entity("CVAPIService.Domain.Entities.Level", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.ToTable("Levels");
                });

            modelBuilder.Entity("CVAPIService.Domain.Entities.Part", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.ToTable("Parts");
                });

            modelBuilder.Entity("CVAPIService.Domain.Entities.RecruitmentStage", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("Name")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.ToTable("RecruitmentStages");
                });

            modelBuilder.Entity("CVAPIService.Domain.Entities.RecruitmentStageDetail", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<int>("CandidateId")
                        .HasColumnType("integer");

                    b.Property<DateOnly?>("Date")
                        .HasColumnType("date");

                    b.Property<string>("Note")
                        .HasColumnType("text");

                    b.Property<int>("RecruitmentStageId")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("CandidateId");

                    b.HasIndex("RecruitmentStageId");

                    b.ToTable("RecruitmentStageDetails");
                });

            modelBuilder.Entity("CVAPIService.Domain.Entities.Status", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.ToTable("Statuses");
                });

            modelBuilder.Entity("CVAPIService.Domain.Entities.WorkingForm", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.ToTable("WorkingForms");
                });

            modelBuilder.Entity("CVAPIService.Infrastructure.Identity.ApplicationUser", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<int>("AccessFailedCount")
                        .HasColumnType("integer");

                    b.Property<string>("ConcurrencyStamp")
                        .IsConcurrencyToken()
                        .HasColumnType("text");

                    b.Property<string>("Email")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<bool>("EmailConfirmed")
                        .HasColumnType("boolean");

                    b.Property<bool>("LockoutEnabled")
                        .HasColumnType("boolean");

                    b.Property<DateTimeOffset?>("LockoutEnd")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("NormalizedEmail")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<string>("NormalizedUserName")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<string>("PasswordHash")
                        .HasColumnType("text");

                    b.Property<string>("PhoneNumber")
                        .HasColumnType("text");

                    b.Property<bool>("PhoneNumberConfirmed")
                        .HasColumnType("boolean");

                    b.Property<string>("SecurityStamp")
                        .HasColumnType("text");

                    b.Property<bool>("TwoFactorEnabled")
                        .HasColumnType("boolean");

                    b.Property<string>("UserName")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.HasKey("Id");

                    b.HasIndex("NormalizedEmail")
                        .HasDatabaseName("EmailIndex");

                    b.HasIndex("NormalizedUserName")
                        .IsUnique()
                        .HasDatabaseName("UserNameIndex");

                    b.ToTable("AspNetUsers", (string)null);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityRole", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<string>("ConcurrencyStamp")
                        .IsConcurrencyToken()
                        .HasColumnType("text");

                    b.Property<string>("Name")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<string>("NormalizedName")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.HasKey("Id");

                    b.HasIndex("NormalizedName")
                        .IsUnique()
                        .HasDatabaseName("RoleNameIndex");

                    b.ToTable("AspNetRoles", (string)null);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityRoleClaim<string>", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("ClaimType")
                        .HasColumnType("text");

                    b.Property<string>("ClaimValue")
                        .HasColumnType("text");

                    b.Property<string>("RoleId")
                        .IsRequired()
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("RoleId");

                    b.ToTable("AspNetRoleClaims", (string)null);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserClaim<string>", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("ClaimType")
                        .HasColumnType("text");

                    b.Property<string>("ClaimValue")
                        .HasColumnType("text");

                    b.Property<string>("UserId")
                        .IsRequired()
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("UserId");

                    b.ToTable("AspNetUserClaims", (string)null);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserLogin<string>", b =>
                {
                    b.Property<string>("LoginProvider")
                        .HasColumnType("text");

                    b.Property<string>("ProviderKey")
                        .HasColumnType("text");

                    b.Property<string>("ProviderDisplayName")
                        .HasColumnType("text");

                    b.Property<string>("UserId")
                        .IsRequired()
                        .HasColumnType("text");

                    b.HasKey("LoginProvider", "ProviderKey");

                    b.HasIndex("UserId");

                    b.ToTable("AspNetUserLogins", (string)null);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserRole<string>", b =>
                {
                    b.Property<string>("UserId")
                        .HasColumnType("text");

                    b.Property<string>("RoleId")
                        .HasColumnType("text");

                    b.HasKey("UserId", "RoleId");

                    b.HasIndex("RoleId");

                    b.ToTable("AspNetUserRoles", (string)null);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserToken<string>", b =>
                {
                    b.Property<string>("UserId")
                        .HasColumnType("text");

                    b.Property<string>("LoginProvider")
                        .HasColumnType("text");

                    b.Property<string>("Name")
                        .HasColumnType("text");

                    b.Property<string>("Value")
                        .HasColumnType("text");

                    b.HasKey("UserId", "LoginProvider", "Name");

                    b.ToTable("AspNetUserTokens", (string)null);
                });

            modelBuilder.Entity("CVAPIService.Domain.Entities.Candidate", b =>
                {
                    b.HasOne("CVAPIService.Domain.Entities.CVSource", "CVSource")
                        .WithMany("Candidates")
                        .HasForeignKey("CVSourceId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("CVAPIService.Domain.Entities.JobDetail", "JobDetail")
                        .WithMany("Candidates")
                        .HasForeignKey("JobDetailId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("CVSource");

                    b.Navigation("JobDetail");
                });

            modelBuilder.Entity("CVAPIService.Domain.Entities.CandidateAttribute", b =>
                {
                    b.HasOne("CVAPIService.Domain.Entities.AttributeDefinition", "AttributeDefinition")
                        .WithMany("CandidateAttributes")
                        .HasForeignKey("AttributeDefinitionId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("CVAPIService.Domain.Entities.Candidate", "Candidate")
                        .WithMany("CandidateAttributes")
                        .HasForeignKey("CandidateId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("AttributeDefinition");

                    b.Navigation("Candidate");
                });

            modelBuilder.Entity("CVAPIService.Domain.Entities.JobDetail", b =>
                {
                    b.HasOne("CVAPIService.Domain.Entities.Address", null)
                        .WithMany("JobDetails")
                        .HasForeignKey("AddressId");

                    b.HasOne("CVAPIService.Domain.Entities.Job", "Job")
                        .WithMany("JobDetails")
                        .HasForeignKey("JobId");

                    b.HasOne("CVAPIService.Domain.Entities.Level", "Level")
                        .WithMany("JobDetails")
                        .HasForeignKey("LevelId");

                    b.HasOne("CVAPIService.Domain.Entities.Part", "Part")
                        .WithMany("JobDetails")
                        .HasForeignKey("PartId");

                    b.HasOne("CVAPIService.Domain.Entities.Status", "Status")
                        .WithMany("JobDetails")
                        .HasForeignKey("StatusId");

                    b.HasOne("CVAPIService.Domain.Entities.WorkingForm", "WorkingForm")
                        .WithMany("JobDetails")
                        .HasForeignKey("WorkingFormId");

                    b.Navigation("Job");

                    b.Navigation("Level");

                    b.Navigation("Part");

                    b.Navigation("Status");

                    b.Navigation("WorkingForm");
                });

            modelBuilder.Entity("CVAPIService.Domain.Entities.JobDetailAddress", b =>
                {
                    b.HasOne("CVAPIService.Domain.Entities.Address", "Address")
                        .WithMany()
                        .HasForeignKey("AddressId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("CVAPIService.Domain.Entities.JobDetail", "JobDetail")
                        .WithMany("JobDetailAddresses")
                        .HasForeignKey("JobDetailId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Address");

                    b.Navigation("JobDetail");
                });

            modelBuilder.Entity("CVAPIService.Domain.Entities.JobDetailAttribute", b =>
                {
                    b.HasOne("CVAPIService.Domain.Entities.AttributeDefinition", "AttributeDefinition")
                        .WithMany("JobDetailAttributes")
                        .HasForeignKey("AttributeDefinitionId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("CVAPIService.Domain.Entities.JobDetail", "JobDetail")
                        .WithMany("JobDetailAttributes")
                        .HasForeignKey("JobDetailId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("AttributeDefinition");

                    b.Navigation("JobDetail");
                });

            modelBuilder.Entity("CVAPIService.Domain.Entities.RecruitmentStageDetail", b =>
                {
                    b.HasOne("CVAPIService.Domain.Entities.Candidate", "Candidate")
                        .WithMany("RecruitmentStageDetails")
                        .HasForeignKey("CandidateId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("CVAPIService.Domain.Entities.RecruitmentStage", "RecruitmentStage")
                        .WithMany("RecruitmentStageDetails")
                        .HasForeignKey("RecruitmentStageId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Candidate");

                    b.Navigation("RecruitmentStage");
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityRoleClaim<string>", b =>
                {
                    b.HasOne("Microsoft.AspNetCore.Identity.IdentityRole", null)
                        .WithMany()
                        .HasForeignKey("RoleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserClaim<string>", b =>
                {
                    b.HasOne("CVAPIService.Infrastructure.Identity.ApplicationUser", null)
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserLogin<string>", b =>
                {
                    b.HasOne("CVAPIService.Infrastructure.Identity.ApplicationUser", null)
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserRole<string>", b =>
                {
                    b.HasOne("Microsoft.AspNetCore.Identity.IdentityRole", null)
                        .WithMany()
                        .HasForeignKey("RoleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("CVAPIService.Infrastructure.Identity.ApplicationUser", null)
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserToken<string>", b =>
                {
                    b.HasOne("CVAPIService.Infrastructure.Identity.ApplicationUser", null)
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("CVAPIService.Domain.Entities.Address", b =>
                {
                    b.Navigation("JobDetails");
                });

            modelBuilder.Entity("CVAPIService.Domain.Entities.AttributeDefinition", b =>
                {
                    b.Navigation("CandidateAttributes");

                    b.Navigation("JobDetailAttributes");
                });

            modelBuilder.Entity("CVAPIService.Domain.Entities.CVSource", b =>
                {
                    b.Navigation("Candidates");
                });

            modelBuilder.Entity("CVAPIService.Domain.Entities.Candidate", b =>
                {
                    b.Navigation("CandidateAttributes");

                    b.Navigation("RecruitmentStageDetails");
                });

            modelBuilder.Entity("CVAPIService.Domain.Entities.Job", b =>
                {
                    b.Navigation("JobDetails");
                });

            modelBuilder.Entity("CVAPIService.Domain.Entities.JobDetail", b =>
                {
                    b.Navigation("Candidates");

                    b.Navigation("JobDetailAddresses");

                    b.Navigation("JobDetailAttributes");
                });

            modelBuilder.Entity("CVAPIService.Domain.Entities.Level", b =>
                {
                    b.Navigation("JobDetails");
                });

            modelBuilder.Entity("CVAPIService.Domain.Entities.Part", b =>
                {
                    b.Navigation("JobDetails");
                });

            modelBuilder.Entity("CVAPIService.Domain.Entities.RecruitmentStage", b =>
                {
                    b.Navigation("RecruitmentStageDetails");
                });

            modelBuilder.Entity("CVAPIService.Domain.Entities.Status", b =>
                {
                    b.Navigation("JobDetails");
                });

            modelBuilder.Entity("CVAPIService.Domain.Entities.WorkingForm", b =>
                {
                    b.Navigation("JobDetails");
                });
#pragma warning restore 612, 618
        }
    }
}
