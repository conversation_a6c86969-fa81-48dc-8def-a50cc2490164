﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace CVAPIService.Infrastructure.Data.Migrations
{
    /// <inheritdoc />
    public partial class Edit_JobDetail_And_FileStorage : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_JobDetails_Jobs_JobId",
                table: "JobDetails");

            migrationBuilder.DropColumn(
                name: "Name",
                table: "JobDetails");

            migrationBuilder.RenameColumn(
                name: "uniqueFileName",
                table: "FileStorages",
                newName: "UniqueFileName");

            migrationBuilder.RenameColumn(
                name: "cvFileName",
                table: "FileStorages",
                newName: "CvFileName");

            migrationBuilder.AlterColumn<int>(
                name: "JobId",
                table: "JobDetails",
                type: "integer",
                nullable: true,
                oldClrType: typeof(int),
                oldType: "integer");

            migrationBuilder.AddColumn<string>(
                name: "JobTitle",
                table: "JobDetails",
                type: "text",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "ToTextCV",
                table: "FileStorages",
                type: "text",
                nullable: true);

            migrationBuilder.AddForeignKey(
                name: "FK_JobDetails_Jobs_JobId",
                table: "JobDetails",
                column: "JobId",
                principalTable: "Jobs",
                principalColumn: "Id");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_JobDetails_Jobs_JobId",
                table: "JobDetails");

            migrationBuilder.DropColumn(
                name: "JobTitle",
                table: "JobDetails");

            migrationBuilder.DropColumn(
                name: "ToTextCV",
                table: "FileStorages");

            migrationBuilder.RenameColumn(
                name: "UniqueFileName",
                table: "FileStorages",
                newName: "uniqueFileName");

            migrationBuilder.RenameColumn(
                name: "CvFileName",
                table: "FileStorages",
                newName: "cvFileName");

            migrationBuilder.AlterColumn<int>(
                name: "JobId",
                table: "JobDetails",
                type: "integer",
                nullable: false,
                defaultValue: 0,
                oldClrType: typeof(int),
                oldType: "integer",
                oldNullable: true);

            migrationBuilder.AddColumn<string>(
                name: "Name",
                table: "JobDetails",
                type: "text",
                nullable: true);

            migrationBuilder.AddForeignKey(
                name: "FK_JobDetails_Jobs_JobId",
                table: "JobDetails",
                column: "JobId",
                principalTable: "Jobs",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }
    }
}
