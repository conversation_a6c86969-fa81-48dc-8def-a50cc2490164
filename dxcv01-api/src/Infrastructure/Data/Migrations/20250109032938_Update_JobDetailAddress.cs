﻿using Microsoft.EntityFrameworkCore.Migrations;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;

#nullable disable

namespace CVAPIService.Infrastructure.Data.Migrations
{
    /// <inheritdoc />
    public partial class Update_JobDetailAddress : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_JobDetails_Addresses_AddressId",
                table: "JobDetails");

            migrationBuilder.AlterColumn<int>(
                name: "AddressId",
                table: "JobDetails",
                type: "integer",
                nullable: true,
                oldClrType: typeof(int),
                oldType: "integer");

            migrationBuilder.CreateTable(
                name: "JobDetailAddresses",
                columns: table => new
                {
                    Id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    JobDetailId = table.Column<int>(type: "integer", nullable: false),
                    AddressId = table.Column<int>(type: "integer", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_JobDetailAddresses", x => x.Id);
                    table.ForeignKey(
                        name: "FK_JobDetailAddresses_Addresses_AddressId",
                        column: x => x.AddressId,
                        principalTable: "Addresses",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_JobDetailAddresses_JobDetails_JobDetailId",
                        column: x => x.JobDetailId,
                        principalTable: "JobDetails",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_JobDetailAddresses_AddressId",
                table: "JobDetailAddresses",
                column: "AddressId");

            migrationBuilder.CreateIndex(
                name: "IX_JobDetailAddresses_JobDetailId",
                table: "JobDetailAddresses",
                column: "JobDetailId");

            migrationBuilder.AddForeignKey(
                name: "FK_JobDetails_Addresses_AddressId",
                table: "JobDetails",
                column: "AddressId",
                principalTable: "Addresses",
                principalColumn: "Id");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_JobDetails_Addresses_AddressId",
                table: "JobDetails");

            migrationBuilder.DropTable(
                name: "JobDetailAddresses");

            migrationBuilder.AlterColumn<int>(
                name: "AddressId",
                table: "JobDetails",
                type: "integer",
                nullable: false,
                defaultValue: 0,
                oldClrType: typeof(int),
                oldType: "integer",
                oldNullable: true);

            migrationBuilder.AddForeignKey(
                name: "FK_JobDetails_Addresses_AddressId",
                table: "JobDetails",
                column: "AddressId",
                principalTable: "Addresses",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }
    }
}
