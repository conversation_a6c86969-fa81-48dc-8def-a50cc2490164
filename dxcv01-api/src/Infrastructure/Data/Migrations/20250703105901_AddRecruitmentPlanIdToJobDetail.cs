﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace CVAPIService.Infrastructure.Data.Migrations
{
    /// <inheritdoc />
    public partial class AddRecruitmentPlanIdToJobDetail : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_JobDetails_Levels_LevelId",
                table: "JobDetails");

            migrationBuilder.DropForeignKey(
                name: "FK_JobDetails_Parts_PartId",
                table: "JobDetails");

            migrationBuilder.AlterColumn<int>(
                name: "Quantity",
                table: "JobDetails",
                type: "integer",
                nullable: true,
                oldClrType: typeof(int),
                oldType: "integer");

            migrationBuilder.AlterColumn<int>(
                name: "PartId",
                table: "JobDetails",
                type: "integer",
                nullable: true,
                oldClrType: typeof(int),
                oldType: "integer");

            migrationBuilder.AlterColumn<int>(
                name: "LevelId",
                table: "JobDetails",
                type: "integer",
                nullable: true,
                oldClrType: typeof(int),
                oldType: "integer");

            migrationBuilder.AlterColumn<string>(
                name: "JobTitle",
                table: "JobDetails",
                type: "text",
                nullable: true,
                oldClrType: typeof(string),
                oldType: "text");

            migrationBuilder.AddColumn<int>(
                name: "RecruitmentPlanId",
                table: "JobDetails",
                type: "integer",
                nullable: true);

            migrationBuilder.AddForeignKey(
                name: "FK_JobDetails_Levels_LevelId",
                table: "JobDetails",
                column: "LevelId",
                principalTable: "Levels",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_JobDetails_Parts_PartId",
                table: "JobDetails",
                column: "PartId",
                principalTable: "Parts",
                principalColumn: "Id");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_JobDetails_Levels_LevelId",
                table: "JobDetails");

            migrationBuilder.DropForeignKey(
                name: "FK_JobDetails_Parts_PartId",
                table: "JobDetails");

            migrationBuilder.DropColumn(
                name: "RecruitmentPlanId",
                table: "JobDetails");

            migrationBuilder.AlterColumn<int>(
                name: "Quantity",
                table: "JobDetails",
                type: "integer",
                nullable: false,
                defaultValue: 0,
                oldClrType: typeof(int),
                oldType: "integer",
                oldNullable: true);

            migrationBuilder.AlterColumn<int>(
                name: "PartId",
                table: "JobDetails",
                type: "integer",
                nullable: false,
                defaultValue: 0,
                oldClrType: typeof(int),
                oldType: "integer",
                oldNullable: true);

            migrationBuilder.AlterColumn<int>(
                name: "LevelId",
                table: "JobDetails",
                type: "integer",
                nullable: false,
                defaultValue: 0,
                oldClrType: typeof(int),
                oldType: "integer",
                oldNullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "JobTitle",
                table: "JobDetails",
                type: "text",
                nullable: false,
                defaultValue: "",
                oldClrType: typeof(string),
                oldType: "text",
                oldNullable: true);

            migrationBuilder.AddForeignKey(
                name: "FK_JobDetails_Levels_LevelId",
                table: "JobDetails",
                column: "LevelId",
                principalTable: "Levels",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_JobDetails_Parts_PartId",
                table: "JobDetails",
                column: "PartId",
                principalTable: "Parts",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }
    }
}
