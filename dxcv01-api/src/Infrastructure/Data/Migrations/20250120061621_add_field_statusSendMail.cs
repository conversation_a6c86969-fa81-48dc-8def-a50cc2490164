﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace CVAPIService.Infrastructure.Data.Migrations
{
    /// <inheritdoc />
    public partial class add_field_statusSendMail : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "IsSendMail",
                table: "Candidates");

            migrationBuilder.AddColumn<string>(
                name: "statusSendMail",
                table: "Candidates",
                type: "text",
                nullable: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "statusSendMail",
                table: "Candidates");

            migrationBuilder.AddColumn<bool>(
                name: "IsSendMail",
                table: "Candidates",
                type: "boolean",
                nullable: false,
                defaultValue: false);
        }
    }
}
