<?php

namespace App\Filament\Resources\RecruitmentPlanResource\Pages;

use App\Filament\Resources\RecruitmentPlanResource;
use App\Models\RecruitmentPlan;
use App\Models\User;
use App\Enums\PermissionEnum;
use Filament\Actions;
use Filament\Forms;
use Filament\Infolists;
use Filament\Infolists\Components;
use Filament\Infolists\Infolist;
use Filament\Resources\Pages\ViewRecord;
use Illuminate\Support\Facades\Auth;

class ViewRecruitmentPlan extends ViewRecord
{
    protected static string $resource = RecruitmentPlanResource::class;

    public function mount(int | string $record): void
    {
        parent::mount($record);

        // Load interview rounds với eager loading để hiển thị (bao gồm criterias)
        $this->record->load([
            'interviewRounds' => function ($query) {
                $query->orderBy('round_number');
            },
            'interviewRounds.criterias' => function ($query) {
                $query->orderBy('sort_order');
            },
            'recruitmentRequest',
            'recruitmentRequest.requester', // Thêm eager loading cho người yêu cầu
            'recruitmentRequest.department', // Thêm eager loading cho phòng ban
            'candidates' => function ($query) {
                $query->orderBy('created_at', 'desc');
            },
            'interviews' => function ($query) {
                $query->with(['candidate', 'interviewRound'])
                    ->orderBy('interview_date', 'desc');
            }
        ]);
    }

    public function infolist(Infolist $infolist): Infolist
    {
        /** @var \App\Models\User|null $user */
        $user = Auth::user();
        $canViewSummary = $user && $user->hasPermission(PermissionEnum::VIEW_RECRUITMENT_PLAN_SUMMARY->value);
        $canViewDetails = $user && $user->hasPermission(PermissionEnum::VIEW_RECRUITMENT_PLAN_DETAILS->value);

        // Nếu không có quyền nào
        if (!$canViewSummary && !$canViewDetails) {
            return $infolist->schema([
                Components\Section::make('Không có quyền truy cập')
                    ->description('Bạn không có quyền xem thông tin kế hoạch tuyển dụng này.')
                    ->schema([])
            ]);
        }

        $tabs = [];

        // Tab tóm tắt - hiển thị khi có quyền VIEW_RECRUITMENT_PLAN_SUMMARY
        if ($canViewSummary) {
            $tabs[] = Components\Tabs\Tab::make('summary')
                ->label('Tóm tắt')
                ->icon('heroicon-o-document-text')
                ->schema($this->getSummarySchema());
        }

        // Tab chi tiết - hiển thị khi có quyền VIEW_RECRUITMENT_PLAN_DETAILS
        if ($canViewDetails) {
            $tabs[] = Components\Tabs\Tab::make('details')
                ->label('Chi tiết')
                ->icon('heroicon-o-clipboard-document-list')
                ->schema($this->getDetailSchema());
        }

        return $infolist->schema([
            Components\Tabs::make('plan_tabs')
                ->tabs($tabs)
                ->columnSpanFull()
        ]);
    }

    /**
     * Schema cho tab tóm tắt - chỉ hiển thị 4 trường cần thiết
     */
    protected function getSummarySchema(): array
    {
        return [
            Components\Section::make('Tóm tắt kế hoạch tuyển dụng')
                ->description('Thông tin cơ bản cho DM xem và phê duyệt')
                ->schema([
                    Components\Grid::make(3)
                        ->schema([
                            Components\TextEntry::make('recruitmentRequest.name')
                                ->label('Yêu cầu Tuyển dụng')
                                ->weight('bold')
                                ->color('primary')
                                ->columnSpan(3),

                            Components\TextEntry::make('recruitmentRequest.department.name')
                                ->label('Phòng ban')
                                ->badge()
                                ->color('info')
                                ->getStateUsing(fn($record) => $record->recruitmentRequest?->department?->name ?? 'Chưa có phòng ban'),

                            Components\TextEntry::make('creator.name')
                                ->label('Người yêu cầu')
                                ->badge()
                                ->color('warning')
                                ->getStateUsing(fn($record) => $record->creator?->name ?? 'Chưa có người yêu cầu'),

                            Components\TextEntry::make('confirmer.name')
                                ->label('Người phê duyệt')
                                ->badge()
                                ->color('success')
                                ->getStateUsing(fn($record) => $record->confirmer?->name ?? 'Chưa có người phê duyệt'),

                            Components\TextEntry::make('salary_range')
                                ->label('Mức lương đề xuất (HR)')
                                ->badge()
                                ->color('gray')
                                ->getStateUsing(function ($record) {
                                    $salaryFrom = $record->hr_salary_from;
                                    $salaryTo = $record->hr_salary_to;

                                    if (!$salaryFrom && !$salaryTo) {
                                        return 'Chưa đề xuất';
                                    }

                                    if ($salaryFrom && $salaryTo) {
                                        return number_format($salaryFrom, 0, ',', '.') . ' - ' . number_format($salaryTo, 0, ',', '.') . ' VNĐ';
                                    }

                                    if ($salaryFrom) {
                                        return 'Từ ' . number_format($salaryFrom, 0, ',', '.') . ' VNĐ';
                                    }

                                    return 'Tối đa ' . number_format($salaryTo, 0, ',', '.') . ' VNĐ';
                                })
                                ->columnSpan(2),

                            Components\TextEntry::make('jd_link')
                                ->label('Link JD')
                                ->getStateUsing(function ($record) {
                                    if (empty($record->jd_link)) {
                                        return '<span class="text-gray-400">Chưa có link JD</span>';
                                    }
                                    $preview = \App\Filament\Resources\RecruitmentPlanResource::getUrlPreview($record->jd_link);
                                    return \App\Filament\Resources\RecruitmentPlanResource::renderJdPreview($preview);
                                })
                                ->html()
                                ->columnSpan(3),
                        ]),

                    // Hiển thị vòng phỏng vấn trong tab tóm tắt nằm ngoài section
                    Components\Grid::make(2)
                        ->schema($this->getInterviewRoundsSummarySchema())
                ]),
        ];
    }

    /**
     * Schema cho hiển thị vòng phỏng vấn trong tab tóm tắt
     */
    protected function getInterviewRoundsSummarySchema(): array
    {
        $rounds = $this->record->interviewRounds ?? collect();
        $schemas = [];

        // Nếu không có vòng phỏng vấn nào
        if ($rounds->isEmpty()) {
            $schemas[] = Components\TextEntry::make('no_rounds_summary')
                ->label('Thông tin vòng phỏng vấn')
                ->getStateUsing(fn() => 'Chưa có vòng phỏng vấn nào được thiết lập cho kế hoạch này.')
                ->extraAttributes(['class' => 'text-gray-500 text-center py-8'])
                ->columnSpan(2); // Chiếm cả 2 cột khi không có dữ liệu
        } else {
            // Tạo card cho từng vòng phỏng vấn
            foreach ($rounds as $index => $round) {
                $roundNumber = $index + 1;
                $roundTitle = "Vòng {$roundNumber}";

                if (!empty($round->name)) {
                    $roundTitle .= ": {$round->name}";
                }

                $schemas[] = Components\Section::make($roundTitle)
                    ->description($this->getRoundDescription($round))
                    ->icon($roundNumber === 1 ? 'heroicon-o-user-group' : 'heroicon-o-briefcase')
                    ->schema([
                        // Thông báo tổng trọng số
                        Components\TextEntry::make("round_{$roundNumber}_weight_summary")
                            ->label('')
                            ->getStateUsing(function () use ($round) {
                                $totalWeight = $round->criterias()->sum('weight');

                                if ($totalWeight == 0) {
                                    return "⚠️ Chưa có tiêu chí đánh giá nào";
                                } elseif ($totalWeight == 100) {
                                    return "✅ Tổng trọng số: {$totalWeight}% (Hợp lệ)";
                                } elseif ($totalWeight < 100) {
                                    $remaining = 100 - $totalWeight;
                                    return "⚠️ Tổng trọng số: {$totalWeight}% (Thiếu {$remaining}% để đạt 100%)";
                                } else {
                                    $excess = $totalWeight - 100;
                                    return "❌ Tổng trọng số: {$totalWeight}% (Thừa {$excess}%, cần giảm để về 100%)";
                                }
                            })
                            ->columnSpanFull()
                            ->extraAttributes(['class' => 'text-sm font-medium']),

                        Components\Grid::make(3)
                            ->schema([
                                Components\TextEntry::make("round_{$roundNumber}_duration")
                                    ->label('Thời lượng')
                                    ->getStateUsing(fn() => $round->duration_minutes ? $round->duration_minutes . ' phút' : 'Không xác định')
                                    ->icon('heroicon-o-clock')
                                    ->badge()
                                    ->color('info'),

                                Components\TextEntry::make("round_{$roundNumber}_format")
                                    ->label('Hình thức')
                                    ->getStateUsing(fn() => $this->getFormatDisplay($round->format))
                                    ->icon('heroicon-o-computer-desktop')
                                    ->badge()
                                    ->color($round->format === 'online' ? 'success' : ($round->format === 'offline' ? 'warning' : 'info')),

                                Components\TextEntry::make("round_{$roundNumber}_interviewers_count")
                                    ->label('Số người PV')
                                    ->getStateUsing(fn() => $this->getInterviewersCount($round))
                                    ->icon('heroicon-o-users')
                                    ->badge()
                                    ->color('primary'),
                            ]),

                        // Hiển thị danh sách tiêu chí đánh giá
                        Components\TextEntry::make("round_{$roundNumber}_criterias")
                            ->label('Tiêu chí đánh giá')
                            ->getStateUsing(function () use ($round) {
                                $criterias = $round->criterias()->orderBy('sort_order')->get();

                                if ($criterias->isEmpty()) {
                                    return 'Chưa có tiêu chí đánh giá nào';
                                }

                                $criteriaList = [];
                                foreach ($criterias as $criteria) {
                                    $required = $criteria->is_required ? ' (Bắt buộc)' : ' (Tùy chọn)';
                                    $criteriaList[] = "• {$criteria->name} - Trọng số: {$criteria->weight}% - Điểm tối đa: {$criteria->max_score} - {$required}\n";
                                }

                                return implode("\n", $criteriaList);
                            })
                            ->markdown()
                            ->columnSpanFull()
                            ->visible(function () use ($round) {
                                return $round->criterias()->exists();
                            }),

                        Components\TextEntry::make("round_{$roundNumber}_interviewers")
                            ->label(__("widgets.interview_calendar.interviewer"))
                            ->getStateUsing(fn() => $this->getInterviewerNames($round))
                            ->badge()
                            ->columnSpanFull()
                            ->visible(fn() => !empty($this->getInterviewerNames($round))),

                        Components\TextEntry::make("round_{$roundNumber}_description")
                            ->label(__("resources.candidate_evaluation.fields.description"))
                            ->getStateUsing(fn() => $round->description ?: 'Không có mô tả')
                            ->markdown()
                            ->columnSpanFull()
                            ->visible(fn() => !empty($round->description)),
                    ])
                    ->collapsible()
                    ->collapsed(false)
                    ->columnSpan(1); // Mỗi card chiếm 1 cột
            }

            // Thêm gợi ý nếu chỉ có 1 vòng
            if ($rounds->count() === 1) {
                $schemas[] = Components\Section::make('Vòng 2')
                    ->description('Chưa thiết lập vòng phỏng vấn thứ 2')
                    ->icon('heroicon-o-plus-circle')
                    ->schema([
                        Components\TextEntry::make('round_2_suggestion')
                            ->label('')
                            ->getStateUsing(fn() => 'Bạn có thể thêm vòng phỏng vấn thứ 2 bằng cách chỉnh sửa kế hoạch tuyển dụng.')
                            ->extraAttributes(['class' => 'text-gray-500 text-center py-4'])
                    ])
                    ->collapsible()
                    ->collapsed(true)
                    ->columnSpan(1); // Chiếm 1 cột để xếp ngang với vòng 1
            }
        }

        return $schemas;
    }

    /**
     * Schema cho tab chi tiết - hiển thị đầy đủ thông tin
     */
    protected function getDetailSchema(): array
    {
        return [
            Components\Section::make(__('recruitment.sections.main_info'))
                ->schema([
                    Components\Grid::make(2)
                        ->schema([
                            Components\TextEntry::make('recruitmentRequest.name')
                                ->label(__('recruitment.recruitment_request'))
                                ->weight('bold')
                                ->color('primary'),

                            Components\TextEntry::make('status')
                                ->label(__('recruitment.status'))
                                ->badge()
                                ->color(fn(string $state): string => match ($state) {
                                    RecruitmentPlan::STATUS_WAITING => 'warning',
                                    RecruitmentPlan::STATUS_APPROVED => 'success',
                                    RecruitmentPlan::STATUS_REJECTED => 'danger',
                                    RecruitmentPlan::STATUS_RECRUITING => 'primary',
                                    RecruitmentPlan::STATUS_DONE => 'success',
                                    default => 'gray',
                                })
                                ->formatStateUsing(fn(string $state): string =>
                                RecruitmentPlan::getStatusOptions()[$state] ?? $state),
                        ]),
                ]),

            Components\Section::make(__('recruitment.sections.department_user_info'))
                ->schema([
                    Components\Grid::make(3)
                        ->schema([
                            Components\TextEntry::make('recruitmentRequest.department.name')
                                ->label('Phòng ban')
                                ->badge()
                                ->color('info')
                                ->getStateUsing(fn($record) => $record->recruitmentRequest?->department?->name ?? 'Chưa có phòng ban'),

                            Components\TextEntry::make('creator.name')
                                ->label(__('recruitment.requested_by'))->badge(),

                            Components\TextEntry::make('confirmer.name')
                                ->label(__('recruitment.approved_by'))->badge(),
                        ]),
                ]),

            Components\Section::make(__('recruitment.sections.recruitment_info'))
                ->schema([
                    Components\Grid::make(2)
                        ->schema([
                            Components\TextEntry::make('hr_salary_from')
                                ->label('Mức lương từ (HR đề xuất)')
                                ->badge()
                                ->color('success')
                                ->formatStateUsing(function ($state) {
                                    return $state ? number_format($state, 0, ',', '.') . ' VNĐ' : 'Chưa đề xuất';
                                }),

                            Components\TextEntry::make('hr_salary_to')
                                ->label('Mức lương đến (HR đề xuất)')
                                ->badge()
                                ->color('success')
                                ->formatStateUsing(function ($state) {
                                    return $state ? number_format($state, 0, ',', '.') . ' VNĐ' : 'Chưa đề xuất';
                                }),
                        ]),

                    Components\Grid::make(2)
                        ->schema([
                            Components\TextEntry::make('recruitmentRequest.salary_from')
                                ->label('Mức lương từ (DM yêu cầu)')
                                ->badge()
                                ->color('warning')
                                ->formatStateUsing(function ($state) {
                                    return $state ? number_format($state, 0, ',', '.') . ' VNĐ' : 'Không xác định';
                                }),

                            Components\TextEntry::make('recruitmentRequest.salary_to')
                                ->label('Mức lương đến (DM yêu cầu)')
                                ->badge()
                                ->color('warning')
                                ->formatStateUsing(function ($state) {
                                    return $state ? number_format($state, 0, ',', '.') . ' VNĐ' : 'Không xác định';
                                }),
                        ]),

                    Components\TextEntry::make('jd_link')
                        ->label(__('recruitment.jd_link'))
                        ->getStateUsing(function ($record) {
                            if (empty($record->jd_link)) {
                                return '<span class="text-gray-400">' . __('recruitment.no_jd_link') . '</span>';
                            }
                            $preview = \App\Filament\Resources\RecruitmentPlanResource::getUrlPreview($record->jd_link);
                            return \App\Filament\Resources\RecruitmentPlanResource::renderJdPreview($preview);
                        })
                        ->html(),
                ]),

            // Hiển thị thông tin các vòng phỏng vấn với 2 card xếp ngang
            Components\Grid::make(2)
                ->schema($this->getInterviewRoundsSchema()),

            // Section thông tin triển khai tuyển dụng
            Components\Section::make('Thông tin triển khai tuyển dụng')
                ->description('Thông tin về ngày tuyển dụng, số lượng và kênh tuyển dụng')
                ->schema([
                    Components\Grid::make(2)
                        ->schema([
                            Components\TextEntry::make('recruitment_start_date')
                                ->label(__('recruitment.start_date'))
                                ->date('d/m/Y')
                                ->placeholder('Chưa xác định'),

                            Components\TextEntry::make('recruitment_close_date')
                                ->label(__('recruitment.close_date'))
                                ->date('d/m/Y')
                                ->placeholder('Chưa xác định'),
                        ]),

                    Components\Grid::make(3)
                        ->schema([
                            Components\TextEntry::make('required_cv_count')
                                ->label(__('recruitment.required_cv_count'))
                                ->badge()
                                ->color('gray')
                                ->icon('heroicon-o-document-text')
                                ->placeholder('0'),

                            Components\TextEntry::make('cv_pass_count')
                                ->label(__('recruitment.cv_pass_count'))
                                ->badge()
                                ->color('success')
                                ->icon('heroicon-o-check-circle')
                                ->placeholder('0'),

                            Components\TextEntry::make('interview_round1_pass_count')
                                ->label(__('recruitment.interview_round1_pass_count'))
                                ->badge()
                                ->color('primary')
                                ->icon('heroicon-o-user-group')
                                ->placeholder('0'),
                        ]),

                    Components\TextEntry::make('recruitment_channels')
                        ->label(__('recruitment.recruitment_channels'))
                        ->getStateUsing(function ($record) {
                            if (empty($record->recruitment_channels)) return [];

                            $channels = $record->recruitment_channels;
                            if (is_string($channels)) {
                                $channels = json_decode($channels, true);
                            }

                            if (!is_array($channels)) {
                                $channels = [$channels];
                            }

                            // Map sang tên hiển thị
                            $options = \App\Models\RecruitmentPlan::getRecruitmentChannelsOptions();
                            return collect($channels)->map(fn($ch) => $options[$ch] ?? $ch)->toArray();
                        })
                        ->badge()
                        ->placeholder('Chưa chọn kênh tuyển dụng'),
                ])
                ->collapsible()
                ->collapsed(false),
        ];
    }

    /**
     * Tạo schema hiển thị 2 vòng phỏng vấn thành 2 card riêng biệt
     */
    protected function getInterviewRoundsSchema(): array
    {
        $rounds = $this->record->interviewRounds ?? collect();
        $schemas = [];

        // Nếu không có vòng phỏng vấn nào
        if ($rounds->isEmpty()) {
            $schemas[] = Components\TextEntry::make('no_rounds_detail')
                ->label('Chi tiết vòng phỏng vấn')
                ->getStateUsing(fn() => 'Chưa có vòng phỏng vấn nào được thiết lập cho kế hoạch này.')
                ->extraAttributes(['class' => 'text-gray-500 text-center py-8'])
                ->columnSpan(2); // Chiếm cả 2 cột khi không có dữ liệu
        } else {
            // Tạo card cho từng vòng phỏng vấn
            foreach ($rounds as $index => $round) {
                $roundNumber = $index + 1;
                $roundTitle = "Vòng {$roundNumber}";

                if (!empty($round->name)) {
                    $roundTitle .= ": {$round->name}";
                }

                $schemas[] = Components\Section::make($roundTitle)
                    ->description($this->getRoundDescription($round))
                    ->icon($roundNumber === 1 ? 'heroicon-o-user-group' : 'heroicon-o-briefcase')
                    ->schema([
                        // Thông báo tổng trọng số
                        Components\TextEntry::make("round_{$roundNumber}_weight_summary")
                            ->label('')
                            ->getStateUsing(function () use ($round) {
                                $totalWeight = $round->criterias()->sum('weight');

                                if ($totalWeight == 0) {
                                    return "⚠️ Chưa có tiêu chí đánh giá nào";
                                } elseif ($totalWeight == 100) {
                                    return "✅ Tổng trọng số: {$totalWeight}% (Hợp lệ)";
                                } elseif ($totalWeight < 100) {
                                    $remaining = 100 - $totalWeight;
                                    return "⚠️ Tổng trọng số: {$totalWeight}% (Thiếu {$remaining}% để đạt 100%)";
                                } else {
                                    $excess = $totalWeight - 100;
                                    return "❌ Tổng trọng số: {$totalWeight}% (Thừa {$excess}%, cần giảm để về 100%)";
                                }
                            })
                            ->columnSpanFull()
                            ->extraAttributes(['class' => 'text-sm font-medium']),

                        Components\Grid::make(3)
                            ->schema([
                                Components\TextEntry::make("round_{$roundNumber}_duration")
                                    ->label('Thời lượng')
                                    ->getStateUsing(fn() => $round->duration_minutes ? $round->duration_minutes . ' phút' : 'Không xác định')
                                    ->icon('heroicon-o-clock')
                                    ->badge()
                                    ->color('info'),

                                Components\TextEntry::make("round_{$roundNumber}_format")
                                    ->label('Hình thức')
                                    ->getStateUsing(fn() => $this->getFormatDisplay($round->format))
                                    ->icon('heroicon-o-computer-desktop')
                                    ->badge()
                                    ->color($round->format === 'online' ? 'success' : ($round->format === 'offline' ? 'warning' : 'info')),

                                Components\TextEntry::make("round_{$roundNumber}_interviewers_count")
                                    ->label('Số người PV')
                                    ->getStateUsing(fn() => $this->getInterviewersCount($round))
                                    ->icon('heroicon-o-users')
                                    ->badge()
                                    ->color('primary'),
                            ]),

                        // Hiển thị danh sách tiêu chí đánh giá
                        Components\TextEntry::make("round_{$roundNumber}_criterias")
                            ->label('Tiêu chí đánh giá')
                            ->getStateUsing(function () use ($round) {
                                $criterias = $round->criterias()->orderBy('sort_order')->get();

                                if ($criterias->isEmpty()) {
                                    return 'Chưa có tiêu chí đánh giá nào';
                                }

                                $criteriaList = [];
                                foreach ($criterias as $criteria) {
                                    $required = $criteria->is_required ? ' (Bắt buộc)' : ' (Tùy chọn)';
                                    $criteriaList[] = "• {$criteria->name} - Trọng số: {$criteria->weight}% - Điểm tối đa: {$criteria->max_score} - {$required}\n";
                                }

                                return implode("\n", $criteriaList);
                            })
                            ->markdown()
                            ->columnSpanFull()
                            ->visible(function () use ($round) {
                                return $round->criterias()->exists();
                            }),

                        Components\TextEntry::make("round_{$roundNumber}_interviewers")
                            ->label(__("widgets.interview_calendar.interviewer"))
                            ->getStateUsing(fn() => $this->getInterviewerNames($round))
                            ->badge()
                            ->columnSpanFull()
                            ->visible(fn() => !empty($this->getInterviewerNames($round))),

                        Components\TextEntry::make("round_{$roundNumber}_description")
                            ->label(__("resources.candidate_evaluation.fields.description"))
                            ->getStateUsing(fn() => $round->description ?: 'Không có mô tả')
                            ->markdown()
                            ->columnSpanFull()
                            ->visible(fn() => !empty($round->description)),
                    ])
                    ->collapsible()
                    ->collapsed(false)
                    ->columnSpan(1); // Mỗi card chiếm 1 cột
            }

            // Thêm gợi ý nếu chỉ có 1 vòng
            if ($rounds->count() === 1) {
                $schemas[] = Components\Section::make('Vòng 2')
                    ->description('Chưa thiết lập vòng phỏng vấn thứ 2')
                    ->icon('heroicon-o-plus-circle')
                    ->schema([
                        Components\TextEntry::make('round_2_suggestion')
                            ->label('')
                            ->getStateUsing(fn() => 'Bạn có thể thêm vòng phỏng vấn thứ 2 bằng cách chỉnh sửa kế hoạch tuyển dụng.')
                            ->extraAttributes(['class' => 'text-gray-500 text-center py-4'])
                    ])
                    ->collapsible()
                    ->collapsed(true)
                    ->columnSpan(1); // Chiếm 1 cột để xếp ngang với vòng 1
            }
        }

        return $schemas;
    }

    /**
     * Lấy mô tả cho vòng phỏng vấn
     */
    protected function getRoundDescription($round): string
    {
        $parts = [];

        if ($round->duration_minutes) {
            $parts[] = "{$round->duration_minutes} phút";
        }

        if ($round->format) {
            $parts[] = $this->getFormatDisplay($round->format);
        }

        $interviewersCount = $this->getInterviewersCount($round);
        if ($interviewersCount > 0) {
            $parts[] = "{$interviewersCount} người phỏng vấn";
        }

        // Thêm thông tin về tiêu chí
        $criteriasCount = $round->criterias()->count();
        if ($criteriasCount > 0) {
            $parts[] = "{$criteriasCount} tiêu chí đánh giá";
        }

        return implode(' • ', $parts) ?: 'Chưa có thông tin chi tiết';
    }

    /**
     * Lấy tên hiển thị cho định dạng phỏng vấn
     */
    protected function getFormatDisplay(?string $format): string
    {
        return match ($format) {
            'online' => 'Trực tuyến',
            'offline' => 'Trực tiếp',
            'hybrid' => 'Linh hoạt',
            default => 'Không xác định'
        };
    }

    /**
     * Đếm số người phỏng vấn
     */
    protected function getInterviewersCount($round): int
    {
        if (empty($round->interviewers)) {
            return 0;
        }
        return count($round->interviewers);
    }

    /**
     * Lấy tên các người phỏng vấn
     */
    protected function getInterviewerNames($round): array
    {
        if (empty($round->interviewers)) {
            return ['Chưa có'];
        }

        $users = User::whereIn('id', $round->interviewers)
            ->where('is_active', true)
            ->pluck('name')
            ->toArray();

        return empty($users) ? ['Chưa có'] : $users;
    }

    protected function getHeaderActions(): array
    {
        return [
            Actions\EditAction::make()
                ->visible(function (): bool {
                    /** @var \App\Models\User|null $user */
                    $user = Auth::user();

                    if (!$user || !$user->hasPermission(PermissionEnum::EDIT_RECRUITMENT_PLANS->value)) {
                        return false;
                    }

                    // Admin có thể sửa trong mọi trạng thái
                    if ($user->hasPermission(PermissionEnum::MANAGE_SYSTEM->value)) {
                        return true;
                    }

                    // User thường chỉ sửa được khi status là waiting
                    return $this->record->status === RecruitmentPlan::STATUS_WAITING;
                }),

            Actions\DeleteAction::make()
                ->visible(function (): bool {
                    /** @var \App\Models\User|null $user */
                    $user = Auth::user();

                    if (!$user || !$user->hasPermission(PermissionEnum::DELETE_RECRUITMENT_PLANS->value)) {
                        return false;
                    }

                    // Admin có thể xóa trong mọi trạng thái
                    if ($user->hasPermission(PermissionEnum::MANAGE_SYSTEM->value)) {
                        return true;
                    }

                    // User thường chỉ xóa được status waiting hoặc rejected
                    return in_array($this->record->status, [RecruitmentPlan::STATUS_WAITING, RecruitmentPlan::STATUS_REJECTED]);
                })
                ->after(function () {
                    \Filament\Notifications\Notification::make()
                        ->title(__('recruitment.notifications.plan_deleted_success'))
                        ->body(__('recruitment.trashed_filter_help'))
                        ->success()
                        ->persistent()
                        ->send();
                })
                ->successRedirectUrl(static::getResource()::getUrl('index')),

            Actions\RestoreAction::make()
                ->label(__('common.restore'))
                ->after(function () {
                    \Filament\Notifications\Notification::make()
                        ->title(__('recruitment.notifications.plan_restored_success'))
                        ->success()
                        ->send();
                }),

            // Gộp các action quản lý ứng viên thành ActionGroup
            Actions\ActionGroup::make([
                Actions\Action::make('view_candidates')
                    ->label('Xem ứng viên')
                    ->icon('heroicon-o-users')
                    ->color('info')
                    ->url(function (): string {
                        return route('filament.admin.resources.candidates.index') . '?tableFilters[recruitment_plan_id][value]=' . $this->record->id;
                    })
                    ->openUrlInNewTab(false)
                    ->visible(function (): bool {
                        /** @var \App\Models\User|null $user */
                        $user = Auth::user();
                        return $user && $user->hasPermission(PermissionEnum::VIEW_CANDIDATES->value);
                    }),

                Actions\Action::make('schedule_interview')
                    ->label('Lên lịch PV')
                    ->icon('heroicon-o-calendar-days')
                    ->color('info')
                    ->visible(
                        fn(): bool =>
                        in_array($this->record->status, [RecruitmentPlan::STATUS_APPROVED, RecruitmentPlan::STATUS_RECRUITING]) &&
                            $this->record->candidates()->count() > 0
                    )
                    ->url(
                        fn(): string =>
                        \App\Filament\Resources\InterviewResource::getUrl('create', [
                            'recruitment_plan_id' => $this->record->id,
                        ])
                    ),

                Actions\Action::make('import_from_cv_bank')
                    ->label('Thêm từ CV Bank')
                    ->icon('heroicon-o-cloud-arrow-down')
                    ->color('primary')
                    ->visible(function (): bool {
                        /** @var \App\Models\User|null $user */
                        $user = Auth::user();
                        return in_array($this->record->status, [
                            RecruitmentPlan::STATUS_RECRUITING,
                            RecruitmentPlan::STATUS_DONE,
                            RecruitmentPlan::STATUS_CANCELLED
                        ]) && $user && $user->hasPermission(PermissionEnum::IMPORT_FROM_CV_BANK->value);
                    })
                    ->url(function (): string {
                        return route('filament.admin.resources.candidates.create', [
                            'recruitment_plan_id' => $this->record->id,
                            'source' => 'cv_bank'
                        ]);
                    }),

                Actions\Action::make('sync_cv_bank')
                    ->label('Đồng bộ CV Bank')
                    ->icon('heroicon-o-arrow-down-tray')
                    ->color('success')
                    ->visible(function (): bool {
                        /** @var \App\Models\User|null $user */
                        $user = Auth::user();
                        return in_array($this->record->status, [
                            RecruitmentPlan::STATUS_RECRUITING,
                            RecruitmentPlan::STATUS_DONE,
                            RecruitmentPlan::STATUS_CANCELLED
                        ]) && $user && $user->hasPermission(PermissionEnum::SYNC_CV_BANK_DATA->value);
                    })
                    ->requiresConfirmation()
                    ->modalHeading('Đồng bộ ứng viên từ CV Bank')
                    ->modalDescription('Hệ thống sẽ tự động tạo các ứng viên mới từ CV Bank (bỏ qua các ứng viên đã tồn tại).')
                    ->modalSubmitActionLabel('Đồng bộ')
                    ->action(function () {
                        try {
                            $candidatesBeforeSync = \App\Models\Candidate::where('recruitment_plan_id', $this->record->id)->count();
                            $exitCode = \Illuminate\Support\Facades\Artisan::call('recruitment:sync-cv-bank', [
                                '--plan-id' => $this->record->id
                            ]);

                            if ($exitCode === 0) {
                                $candidatesAfterSync = \App\Models\Candidate::where('recruitment_plan_id', $this->record->id)->count();
                                $newCandidatesCount = $candidatesAfterSync - $candidatesBeforeSync;

                                \Filament\Notifications\Notification::make()
                                    ->title('Đồng bộ thành công')
                                    ->body("Đã tạo mới {$newCandidatesCount} ứng viên từ CV Bank.")
                                    ->success()
                                    ->send();
                            } else {
                                \Filament\Notifications\Notification::make()
                                    ->title('Đồng bộ thất bại')
                                    ->danger()
                                    ->send();
                            }
                        } catch (\Exception $e) {
                            \Filament\Notifications\Notification::make()
                                ->title('Lỗi đồng bộ')
                                ->body($e->getMessage())
                                ->danger()
                                ->send();
                        }
                    }),
            ])
                ->label('Quản lý ứng viên')
                ->icon('heroicon-o-users')
                ->color('info')
                ->button(),

            // Action phê duyệt
            Actions\Action::make('approve')
                ->label('Phê duyệt')
                ->icon('heroicon-o-check-circle')
                ->color('success')
                ->requiresConfirmation()
                ->modalHeading('Phê duyệt kế hoạch tuyển dụng')
                ->modalDescription('Bạn có chắc chắn muốn phê duyệt kế hoạch tuyển dụng này?')
                ->modalSubmitActionLabel('Phê duyệt')
                ->action(function () {
                    $this->record->approve(Auth::user());

                    \Filament\Notifications\Notification::make()
                        ->title('Đã phê duyệt kế hoạch tuyển dụng')
                        ->success()
                        ->send();
                })
                ->visible(function (): bool {
                    /** @var \App\Models\User|null $user */
                    $user = Auth::user();
                    return $this->record->canBeApproved() && $user && $user->hasPermission(PermissionEnum::APPROVE_RECRUITMENT_PLANS->value);
                }),

            // Action yêu cầu chỉnh sửa
            Actions\Action::make('request_revision')
                ->label('Yêu cầu chỉnh sửa')
                ->icon('heroicon-o-arrow-path')
                ->color('warning')
                ->form([
                    Forms\Components\Textarea::make('reason')
                        ->label('Lý do yêu cầu chỉnh sửa')
                        ->required()
                        ->maxLength(500)
                        ->rows(3)
                ])
                ->modalHeading('Yêu cầu chỉnh sửa kế hoạch tuyển dụng')
                ->modalSubmitActionLabel('Yêu cầu chỉnh sửa')
                ->action(function (array $data) {
                    $this->record->requestRevision(Auth::user(), $data['reason']);

                    \Filament\Notifications\Notification::make()
                        ->title('Đã yêu cầu chỉnh sửa kế hoạch tuyển dụng')
                        ->warning()
                        ->send();
                })
                ->visible(function (): bool {
                    /** @var \App\Models\User|null $user */
                    $user = Auth::user();
                    return $this->record->status === RecruitmentPlan::STATUS_WAITING && $user && $user->hasPermission(PermissionEnum::REQUEST_REVISION_RECRUITMENT_PLANS->value);
                }),

            // Action bắt đầu tuyển dụng
            Actions\Action::make('start_recruiting')
                ->label('Bắt đầu tuyển dụng')
                ->icon('heroicon-o-play')
                ->color('info')
                ->form([
                    Forms\Components\Grid::make(2)
                        ->schema([
                            Forms\Components\DatePicker::make('recruitment_start_date')
                                ->label('Ngày mở tuyển')
                                ->default($this->record->recruitment_start_date)
                                ->required()
                                ->minDate(now()->format('Y-m-d'))
                                ->helperText('Ngày mở tuyển phải từ hôm nay trở đi'),

                            Forms\Components\DatePicker::make('recruitment_close_date')
                                ->label('Ngày đóng tuyển')
                                ->default($this->record->recruitment_close_date)
                                ->required()
                                ->after('recruitment_start_date')
                                ->helperText('Ngày đóng tuyển phải sau ngày mở tuyển'),
                        ]),

                    Forms\Components\Grid::make(3)
                        ->schema([
                            Forms\Components\TextInput::make('required_cv_count')
                                ->label('Số CV cần thu thập')
                                ->numeric()
                                ->default($this->record->required_cv_count ?? 50)
                                ->required()
                                ->minValue(1)
                                ->maxValue(1000)
                                ->helperText('Số CV cần thu thập (1-1000)'),

                            Forms\Components\TextInput::make('cv_pass_count')
                                ->label('Số CV đạt yêu cầu')
                                ->numeric()
                                ->default($this->record->cv_pass_count ?? 20)
                                ->required()
                                ->minValue(1)
                                ->live()
                                ->afterStateUpdated(function ($state, $set, $get) {
                                    $requiredCvCount = $get('required_cv_count');
                                    if ($requiredCvCount && $state > $requiredCvCount) {
                                        $set('cv_pass_count', $requiredCvCount);
                                    }
                                })
                                ->helperText('Số CV đạt yêu cầu (≤ số CV cần thu thập)'),

                            Forms\Components\TextInput::make('interview_round1_pass_count')
                                ->label('Số ứng viên vào vòng 1')
                                ->numeric()
                                ->default($this->record->interview_round1_pass_count ?? 10)
                                ->required()
                                ->minValue(1)
                                ->live()
                                ->afterStateUpdated(function ($state, $set, $get) {
                                    $cvPassCount = $get('cv_pass_count');
                                    if ($cvPassCount && $state > $cvPassCount) {
                                        $set('interview_round1_pass_count', $cvPassCount);
                                    }
                                })
                                ->helperText('Số ứng viên vào vòng 1 (≤ số CV đạt yêu cầu)'),
                        ]),

                    Forms\Components\CheckboxList::make('recruitment_channels')
                        ->label('Kênh tuyển dụng')
                        ->options(RecruitmentPlan::getRecruitmentChannelsOptions())
                        ->default($this->record->recruitment_channels ?? [])
                        ->required()
                        ->columns(3)
                        ->gridDirection('row'),
                ])
                ->modalHeading('Bắt đầu tuyển dụng')
                ->modalDescription('Thiết lập thông tin triển khai tuyển dụng')
                ->modalSubmitActionLabel('Bắt đầu tuyển dụng')
                ->modalCancelActionLabel('Hủy')
                ->action(function (array $data) {
                    // Cập nhật thông tin recruitment plan với dữ liệu từ form
                    $this->record->update([
                        'status' => RecruitmentPlan::STATUS_RECRUITING,
                        'recruitment_start_date' => $data['recruitment_start_date'],
                        'recruitment_close_date' => $data['recruitment_close_date'],
                        'required_cv_count' => $data['required_cv_count'],
                        'cv_pass_count' => $data['cv_pass_count'],
                        'interview_round1_pass_count' => $data['interview_round1_pass_count'],
                        'recruitment_channels' => $data['recruitment_channels'],
                    ]);

                    // Cập nhật trạng thái request sang recruiting
                    if ($this->record->recruitmentRequest) {
                        $this->record->recruitmentRequest->update([
                            'status' => \App\Models\RecruitmentRequest::STATUS_RECRUITING,
                        ]);
                    }

                    // Gửi thông báo cho DM, người phỏng vấn, Giám đốc PED
                    app(\App\Services\RecruitmentNotificationService::class)
                        ->notifyOnRecruitmentStarted($this->record, \Illuminate\Support\Facades\Auth::user());

                    \Filament\Notifications\Notification::make()
                        ->title('Đã bắt đầu tuyển dụng')
                        ->body('Kế hoạch tuyển dụng đã được cập nhật và bắt đầu quá trình tuyển dụng.')
                        ->success()
                        ->send();
                })
                ->visible(function (): bool {
                    /** @var \App\Models\User|null $user */
                    $user = Auth::user();
                    return $this->record->status === RecruitmentPlan::STATUS_APPROVED && $user && $user->hasPermission(PermissionEnum::EDIT_RECRUITMENT_PLANS->value);
                }),

            // Action hoàn thành tuyển dụng
            Actions\Action::make('complete_recruiting')
                ->label('Hoàn thành tuyển dụng')
                ->icon('heroicon-o-check-badge')
                ->color('success')
                ->requiresConfirmation()
                ->modalHeading('Hoàn thành tuyển dụng')
                ->modalDescription('Bạn có chắc chắn muốn hoàn thành quá trình tuyển dụng cho kế hoạch này?')
                ->modalSubmitActionLabel('Hoàn thành')
                ->action(function () {
                    // Cập nhật trạng thái plan
                    $this->record->update([
                        'status' => RecruitmentPlan::STATUS_DONE,
                    ]);

                    // Cập nhật trạng thái request về done
                    if ($this->record->recruitmentRequest) {
                        $this->record->recruitmentRequest->update([
                            'status' => \App\Models\RecruitmentRequest::STATUS_DONE,
                        ]);
                    }

                    // Gửi thông báo hoàn thành cho DM, PED Director và các bên liên quan
                    app(\App\Services\RecruitmentNotificationService::class)
                        ->notifyOnRecruitmentCompleted($this->record, \Illuminate\Support\Facades\Auth::user());

                    \Filament\Notifications\Notification::make()
                        ->title('Đã hoàn thành quá trình tuyển dụng')
                        ->success()
                        ->send();
                })
                ->visible(function (): bool {
                    /** @var \App\Models\User|null $user */
                    $user = Auth::user();
                    return $this->record->status === RecruitmentPlan::STATUS_RECRUITING && $user && $user->hasPermission(PermissionEnum::EDIT_RECRUITMENT_PLANS->value);
                }),
        ];
    }
}
